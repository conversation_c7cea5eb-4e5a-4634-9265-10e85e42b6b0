--[[
	VULTAIC MGM - MUSIC RENDSZER SZERVER OLDAL

	Ez a fájl a music (zene) rendszer szerver oldali logikáját tartalmazza.

	Főbb funkciók:
	- Live stream értesítések
	- Stream státusz kezelése
	- Chat üzenetek küldése stream eseményekről
	- Streamer információk megjelenítése

	Működés:
	1. Stream online esemény fogadása
	2. Globális chat üzenet küldése
	3. Streamer név megjelenítése
	4. Hangol<PERSON><PERSON> instrukciók (M gomb)

	Ez egy egyszerű értesítési rendszer a live streamekhez.
]]

-- Stream verifikáció event definíció
addEvent("Music:onStreamVerify", true)

--[[
	Stream verifikáció kezelése

	@param string streamer - A streamer neve

	Amikor egy live stream online lesz, globális üzenetet küld
	minden játékosnak a streamer nevével és hangolási instrukcióval.
]]
addEventHandler("Music:onStreamVerify", resourceRoot, function(streamer)
	outputChatBox("#00ff00[Live Stream] #ffffffLive stream is online! Streamer: "..streamer.."#ffffff. M -> Stream to tune in!", root, 255, 255, 255, true)
end)