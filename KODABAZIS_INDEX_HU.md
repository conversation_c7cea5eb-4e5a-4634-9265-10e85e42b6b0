# Vultaic MGM Kódbázis Teljes Index

## Projekt Áttekintés

**Projekt neve:** Vultaic Multigamemode for MTA:SA  
**Szerzők:** DizzasTeR & MIRAGE  
**Licenc:** MIT License (2020)  
**Platform:** Multi Theft Auto: San Andreas (MTA:SA)  
**<PERSON><PERSON><PERSON><PERSON> frissítés:** 2025-08-11

---

## 📁 Főkönyvtár Struktú<PERSON>

```
MGM-Vultaic/
├── [admin]/           # Admin és moderáci<PERSON> rends<PERSON>ek (435 fájl)
├── [sync]/            # Szinkronizációs modulok (4 fájl)
├── [vultaic]/         # Fő játékmód modulok (500+ fájl)
├── LICENSE            # MIT licenc
├── README.md          # Projekt leírás
└── SYSTEM_OVERVIEW_HU.md  # Rendszer áttekintés (488 sor)
```

---

## 🎮 [vultaic] - <PERSON>ő Játékmód Modulok

### Core Rendszer
- **`core/`** - <PERSON><PERSON><PERSON><PERSON> rendszer komponensek (5 fájl)
  - `meta.xml` - <PERSON><PERSON> konfiguráció, 42 modul include
  - `common.lua` - Közös függvények
  - `core_server.lua` - Szerver logika
  - `core_client.lua` - Kliens logika  
  - `lobby_server.lua` - Lobby kezelés

### Adatbázis és Hitelesítés
- **`v_mysql/`** - MySQL adatbázis rendszer (5 fájl)
  - `mysql_server.lua` - DB kapcsolat ⚠️ **HARDKÓDOLT JELSZÓ**
  - `CAccount.lua` - Felhasználói fiók osztály
  - `mysql_client.lua` - Kliens oldali DB műveletek

- **`v_login/`** - Bejelentkezési rendszer (13 fájl)
  - `login_server.lua` - API hitelesítés
  - `avatars_server.lua` - Avatar kezelés

- **`v_security/`** - Biztonsági rendszer (2 fájl)
  - `security_server.lua` - Biztonsági ellenőrzések

### Játékmódok
- **`v_race/`** - Verseny mód (8 fájl)
- **`v_dm/`** - Deathmatch (18 fájl)
- **`v_hunter/`** - Hunter mód (5 fájl)
- **`v_hunter_nolimit/`** - Hunter korlátok nélkül (5 fájl)
- **`v_shooter/`** - Shooter mód (6 fájl)
- **`v_shooter_jump/`** - Jumping Shooter (6 fájl)
- **`v_training_dm/`** - DM edzés (9 fájl)
- **`v_training_race/`** - Race edzés (9 fájl)
- **`v_dd/`** - Destruction Derby (3 fájl)
- **`v_fdd/`** - Fun Destruction Derby (7 fájl)
- **`v_hdm/`** - Hunter Deathmatch (10 fájl)

### Jármű Testreszabás ⚠️ **KÓDOLT MODULOK**
- **`v_garage/`** - Garage rendszer (34 fájl)
  - ⚠️ **15+ .luac fájl** - Nem ellenőrizhető kód
  - `garage_client.luac` - Fő garage logika
  - `tabs/` könyvtár - Különböző testreszabási opciók

- **`v_tuning/`** - Tuning rendszer (13 fájl)
- **`v_body/`** - Karosszéria módosítások (5 fájl)
- **`v_paint/`** - Festés rendszer (5 fájl)
- **`v_wheels/`** - Kerék rendszer (21 fájl)
- **`v_lights/`** - Világítás (11 fájl)
- **`v_neons/`** - Neon világítás (11 fájl)
- **`v_overlays/`** - Overlay rendszer (51 fájl)

### UI és Felhasználói Élmény
- **`v_panel/`** - Fő felhasználói panel (37 fájl)
  - ⚠️ **17+ .luac fájl** - Kódolt kliens kód
- **`v_chat/`** - Chat rendszer (4 fájl)
- **`v_scoreboard/`** - Eredménytábla (3 fájl)
- **`v_radar/`** - Minimap radar (5 fájl)
- **`v_racehud/`** - Verseny HUD (4 fájl)
- **`v_speedo/`** - Sebességmérő (2 fájl)
- **`v_deathlist/`** - Halállista (4 fájl)
- **`v_killmessages/`** - Ölési üzenetek (4 fájl)
- **`v_notify/`** - Értesítési rendszer (2 fájl)

### Közösségi Funkciók
- **`v_clans/`** - Klán rendszer (43 fájl)
- **`v_achievements/`** - Eredmények (5 fájl)
- **`v_toptimes/`** - Legjobb idők (4 fájl)
- **`v_donatorship/`** - Támogatói rendszer (21 fájl)

### Kiegészítő Modulok
- **`v_music/`** - Zene lejátszás (4 fájl)
- **`v_shaders/`** - Shader rendszer (5 fájl)
- **`v_smoke/`** - Füst effektek (3 fájl)
- **`v_fade/`** - Fade effektek (3 fájl)
- **`v_info/`** - Információs rendszer (3 fájl)
- **`v_lobby/`** - Lobby kezelés (6 fájl)
- **`v_locale/`** - Nyelvi lokalizáció (5 fájl)
- **`v_settings/`** - Beállítások (3 fájl)
- **`v_avatars/`** - Avatar rendszer (3 fájl)
- **`v_mapmark/`** - Map jelölések (2 fájl)
- **`v_os/`** - Operációs rendszer funkciók (8 fájl)

### Speciális Modulok
- **`[garage_map]/`** - Garage map (2 fájl)
- **`[v_addons]/`** - Kiegészítők (33 fájl)
- **`mapmanager/`** - Map kezelő (15 fájl)
- **`scriptloader/`** - Script betöltő (5 fájl)

---

## 🛡️ [admin] - Adminisztrációs Rendszer

### Admin Modulok
- **`v_admin/`** - Alapvető admin parancsok (7 fájl)
- **`acpanel/`** - Admin Control Panel (15 fájl)
- **`admin/`** - Részletes admin rendszer (67 fájl)
- **`admin2/`** - Fejlett admin rendszer (321 fájl)
- **`runcode/`** - Kód futtatás (fejlesztői) (14 fájl)
- **`ipb/`** - IP Board integráció (11 fájl)

### Admin Jogosultságok
```
Szint 1: Alapfelhasználó
Szint 2: Moderátor (kick, mute, warn)
Szint 3: Super Moderátor (ban, weather, time)
Szint 4: Fejlesztő (map kezelés, események)
Szint 5: Tulajdonos (tag kezelés, teljes hozzáférés)
```

---

## 📊 Fájl Statisztikák

### Fájltípusok Összesítése
- **Lua fájlok:** 102+ darab
- **XML konfigurációk:** 126+ darab
- **Luac kódolt fájlok:** 29 darab ⚠️
- **Összes fájl:** 1000+ darab

### Kódolt Fájlok Részletezése ⚠️

#### v_garage modul (15 .luac fájl):
```
camera_client.luac
dxlib_client.luac
garage_client.luac
util_client.luac
tabs/bodyparts.luac
tabs/colors.luac
tabs/lights.luac
tabs/neons.luac
tabs/overlays.luac
tabs/rocketcolor.luac
tabs/skins.luac
tabs/stickers.luac
tabs/stickers_catalog.luac
tabs/tints.luac
tabs/wheels.luac
```

#### v_panel modul (14 .luac fájl):
```
dxlib_client.luac
panel_client.luac
util_client.luac
tabs/achievements_client.luac
tabs/clans_administration_client.luac
tabs/clans_client.luac
tabs/clans_overview_client.luac
tabs/clans_registration_client.luac
tabs/help_client.luac
tabs/help_details_client.luac
tabs/maps_client.luac
tabs/settings_client.luac
tabs/statistics_client.luac
tabs/statistics_player_client.luac
```

---

## 🔍 Kritikus Modulok Elemzése

### 1. Core Rendszer (`core/`)
**Funkciók:**
- Arena regisztráció és kezelés
- Játékos dimenzió kezelés
- Lobby rendszer
- 42 modul koordinálása

**Exportált függvények:**
- `registerArena()` - Arena regisztráció
- `getPlayerFromID()` - Játékos lekérés ID alapján
- `sendPlayerToLobby()` - Lobby-ba küldés
- Spectating funkciók

### 2. MySQL Rendszer (`v_mysql/`)
**⚠️ KRITIKUS BIZTONSÁGI PROBLÉMA:**
```lua
g_Connection, error = dbConnect("mysql", "dbname="..DB_NAME..";host=127.0.0.1;port=3306", "root", "M1RAg3_Zz@ST3R")
```

**Adatbázis konfiguráció:**
- Host: 127.0.0.1:3306
- Production DB: `v_accounts`
- Development DB: `v_accounts_dev`
- Felhasználó: `root` ⚠️
- Jelszó: `M1RAg3_Zz@ST3R` ⚠️ **HARDKÓDOLT**

### 3. Login Rendszer (`v_login/`)
**API végpontok:**
- Production: `https://forum.vultaic.com/mta_login_test.php`
- Development: `https://forum.vultaic.com/mta_login_dev.php`

**Biztonsági funkciók:**
- Username/password validáció
- Duplikált bejelentkezés megakadályozása
- Session kezelés

### 4. Biztonsági Rendszer (`v_security/`)
**Védelem:**
- Serial szám ellenőrzés
- Nickname validáció (min. 3 karakter)
- Duplikált nevek megakadályozása
- Parancs naplózás
- Debug script hozzáférés korlátozás

**⚠️ Külső kód fordítás:**
```lua
fetchRemote("http://luac.mtasa.com/?compile=1&debug=0&obfuscate=2", ...)
```

---

## 🚨 Biztonsági Kockázatok Összefoglalása

### Azonnali Beavatkozást Igénylő Problémák:

1. **🔴 Hardkódolt Adatbázis Jelszó**
   - Fájl: `v_mysql/mysql_server.lua:17`
   - Kockázat: Teljes adatbázis kompromittálás
   - Megoldás: Környezeti változók használata

2. **🔴 Root Adatbázis Hozzáférés**
   - Túl magas jogosultságok
   - Megoldás: Dedikált felhasználó létrehozása

3. **🟡 Kódolt .luac Fájlok (29 darab)**
   - Nem ellenőrizhető kód
   - Potenciális backdoor lehetőség
   - Érintett modulok: `v_garage`, `v_panel`

4. **🟡 Külső Kód Fordítási Szolgáltatás**
   - HTTP kapcsolat `luac.mtasa.com`-hoz
   - Kártékony kód injektálás lehetősége

### Közepes Kockázatok:

5. **🟡 HTTP API Kapcsolat**
   - HTTPS használata pozitív
   - Jelszavak POST-ban továbbítva

6. **🟡 Naplózás Hiányosságok**
   - Személyes adatok a logokban
   - Nem megfelelő log szűrés

---

## 📈 Rendszer Komplexitás

### Modulok Száma Kategóriánként:
- **Core rendszer:** 5 modul
- **Játékmódok:** 15+ modul
- **UI/UX:** 10+ modul
- **Testreszabás:** 8+ modul
- **Admin rendszer:** 6+ modul
- **Közösségi:** 4+ modul

### Kód Méret Becslés:
- **Összes .lua fájl:** ~102 darab
- **Kódolt .luac fájl:** 29 darab
- **XML konfiguráció:** 126+ darab
- **Becsült kódsorok:** 15,000+ sor

---

## 🔧 Technikai Architektúra

### Kommunikációs Rétegek:
```
Kliens ←→ Core ←→ MySQL
   ↓        ↓       ↓
 UI/UX   Arena   Database
   ↓        ↓       ↓
Modulok  Logic   Stats
```

### Event Rendszer:
- **Szerver ↔ Kliens:** Kétirányú event kommunikáció
- **Resource közötti:** Cross-resource function hívások
- **Trigger rendszer:** Event alapú architektúra

### Külső Függőségek:
- **Forum API:** `forum.vultaic.com` (HTTPS)
- **Luac Service:** `luac.mtasa.com` (HTTP) ⚠️
- **MySQL:** Lokális adatbázis (127.0.0.1)

---

## 📋 Fájl Kategorizálás

### ✅ Biztonságos Fájlok (Ellenőrzött):
- Core server/client logika
- Admin parancsok
- Security modul
- Login rendszer
- Map manager

### ⚠️ Kockázatos Fájlok:
- **29 .luac fájl** - Nem ellenőrizhető
- **mysql_server.lua** - Hardkódolt jelszó
- **security_server.lua** - Külső kód fordítás

### 🔍 Vizsgálandó Területek:
- Garage modul teljes kliens kódja
- Panel rendszer UI logikája
- TEA titkosítási kulcsok
- Admin jogosultság rendszer

---

## 🎯 Következő Lépések

1. **Azonnali javítások:**
   - Adatbázis jelszó externalizálása
   - .luac fájlok dekompilálása
   - Root hozzáférés korlátozása

2. **Biztonsági audit:**
   - Teljes kód áttekintés
   - Penetrációs teszt
   - Sebezhetőség elemzés

3. **Dokumentáció frissítés:**
   - API dokumentáció
   - Biztonsági útmutató
   - Fejlesztői dokumentáció

---

*Ez a dokumentum a teljes kódbázis indexét tartalmazza 2025-08-11 állapot szerint.*
