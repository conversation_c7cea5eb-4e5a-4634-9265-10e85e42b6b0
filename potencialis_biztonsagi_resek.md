# Potenciális Biz<PERSON>á<PERSON> – Vultaic-MGM-master

<PERSON>z a dokumentum összegyűjti a Vultaic rendszer auditja során feltárt vagy poten<PERSON><PERSON> el<PERSON><PERSON>ul<PERSON> biz<PERSON> r<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, vala<PERSON>t a kapcsolódó Lua kódrészletek, magyarázatok és javaslatok listáját. Minden példa magyar nyelvű magyarázattal és audit-kommentárral van ellátva.

---

## 1. Admin jogosultság-ellenőrz<PERSON> hiánya

**P<PERSON>lda (helyes jogosultság-ellenőrz<PERSON>):**
```lua
addCommandHandler("ban", function(player, ...)
    if exports.v_admin:getPlayerAdminLevel(player) < 3 then
        outputChatBox("Nincs jogosultságod!", player)
        return
    end
    -- parancs végrehajtása
end)
```
**Audit megjegyzés:**
A legtöbb admin parancs el<PERSON>tt explicit admin szint ellenőrzés történik. Ha bármelyik parancsnál ez hiányzik, az súlyos jogosultsági rést jelenthet!

---

## 2. Serial, IP és account validáció gyengeségei

**Példa (serial ellenőrzés):**
```lua
if not serial or serial == "" then
    cancelEvent()
    outputChatBox("Serial nélkül nem lehet csatlakozni.", player)
    return
end
```
**Audit megjegyzés:**
A serial/IP ellenőrzés alapvető, de ha valahol hiányzik vagy megkerülhető, az lehetőséget ad hamis kliensek vagy duplikált accountok használatára.

---

## 3. Input validáció hiánya

**Példa (jelszóhossz ellenőrzés):**
```lua
if string.len(password) < 6 then
    outputChatBox("A jelszónak legalább 6 karakter hosszúnak kell lennie.", player)
    return
end
```
**Audit megjegyzés:**
Minden felhasználói inputot szerveroldalon is validálni kell. Ha ez elmarad, SQL injection vagy egyéb támadás válik lehetővé.

---

## 4. Kimenő hálózati forgalom és fetchRemote veszélyei

**Példa:**
```lua
fetchRemote("https://forum.vultaic.com/api/...", ...)
```
**Audit megjegyzés:**
Ha nem csak ismert, saját infrastruktúrához tartozó címekre történik kimenő hívás, vagy ha a válaszokat nem validálja a rendszer, az adatszivárgáshoz vagy támadáshoz vezethet.

---

## 5. Remote code execution (runcode modul)

**Példa:**
```lua
addCommandHandler("runcode", function(player, code)
    if exports.v_admin:getPlayerAdminLevel(player) < 10 then
        outputChatBox("Nincs jogosultságod!", player)
        return
    end
    local func = loadstring(code)
    if func then
        func()
    end
end)
```
**Audit megjegyzés:**
A runcode/debug modul nagyon veszélyes lehet, ha nem csak fejlesztők férhetnek hozzá, vagy nincs szigorú logolás/jogosultságkezelés.

---

## 6. triggerServerEvent/triggerClientEvent visszaélés

**Példa:**
```lua
triggerServerEvent("admin:ban", resourceRoot, target, reason)
```
**Audit megjegyzés:**
Minden szerveroldali eventnél szükséges a jogosultságok szerveroldali ellenőrzése, különben a kliens manipulálhatja a szervert.

---

## 7. Hardcoded jelszó, serial, IP

**Példa:**
```lua
local adminSerial = "1234567890ABCDEF"
```
**Audit megjegyzés:**
Soha ne tárolj jelszót, serialt vagy IP-t hardcode-olva! Ez könnyen visszafejthető és kihasználható.

---

## 8. Javaslatok minden részhez
- Központi jogosultság-ellenőrző függvény használata minden admin funkcióhoz.
- Minden felhasználói input szerveroldali validálása.
- Kimenő forgalom naplózása, célcímek whitelistje.
- runcode/debug modul szigorú logolása, csak fejlesztői hozzáférés.
- triggerEventek szerveroldali validációja.
- Hardcoded érzékeny adatok teljes tilalma.

---

## 9. Központi szerveroldali input validáció Lua-ban

**Példa – Egységes validációs függvény:**
```lua
-- Egyszerű szerveroldali input szűrő függvény
function sanitizeString(str, minLen, maxLen)
    if type(str) ~= "string" then return false, "Érvénytelen típus!" end
    if string.len(str) < (minLen or 1) or string.len(str) > (maxLen or 32) then
        return false, "Hibás hossz!"
    end
    -- Tiltott karakterek szűrése (pl. speciális karakterek, szkriptek)
    if string.find(str, "[<>\"'%;]", 1, false) then
        return false, "Tiltott karakter!"
    end
    -- SQL kulcsszavak szűrése (opcionális)
    local lower = string.lower(str)
    if string.find(lower, "select ") or string.find(lower, "insert ") or string.find(lower, "update ") or string.find(lower, "delete ") then
        return false, "SQL injection gyanú!"
    end
    return true, str
end

-- Használat példa:
local ok, msg = sanitizeString(playerName, 3, 24)
if not ok then
    outputChatBox("Név hiba: "..msg, player)
    return
end
```
**Audit magyarázat:**
- Ez a függvény minden felhasználói string inputra alkalmazható (név, üzenet, email, stb.).
- Ellenőrzi a típust, hosszúságot, tiltott karaktereket, és (opcionálisan) SQL kulcsszavakat is.
- Így minden kritikus ponton (regisztráció, bejelentkezés, chat, parancs) könnyen, egységesen alkalmazható.
- A validáció minimális erőforrást igényel, de jelentősen növeli a biztonságot!

Ha újabb példát vagy konkrét auditot szeretnél, bővítem a dokumentumot! Készült: 2025.08.12.
