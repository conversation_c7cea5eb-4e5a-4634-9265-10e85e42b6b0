--[[
	VULTAIC MGM - PANEL RENDSZER SZERVER OLDAL

	Ez a fájl a panel (F7 menü) rendszer szerver oldali logikáját tartalmazza.

	Főbb funkciók:
	- Notification üzenetek megjelenítése
	- Donator státusz ellenőrzés
	- MySQL adatbá<PERSON> m<PERSON> (wrapper függvények)
	- Játékos statisztikák kezelése
	- Export függvények más resource-ok számára

	Wrapper függvények:
	- setPlayerStats: Játékos statisztika beállítása
	- getPlayerStats: Játékos statisztika lekérése
	- takePlayerStats: Játékos statisztikából levonás
	- isPlayerDonator: Donator státusz ellenőrzés

	Ez a fájl főként utility függvényeket biztosít
	más rendszerek számára.
]]

-- Panel rendszer fő objektuma
panel = {}

--[[
	Üzenet megjelenítése játékosnak

	@param element player - A játékos element
	@param string title - Az üzenet címe
	@param string message - Az üzenet szövege
	@param ... - További paraméterek a notification számára

	Ez a függvény notification eventet triggerel a kliensen.
]]
function panel.displayMessage(player, title, message, ...)
	if isElement(player) then
		triggerClientEvent("notification:create", player, tostring(title), tostring(message), ...)
	end
end

--[[
	Donator státusz ellenőrzése

	@param element player - A játékos element
	@return boolean - True ha donator, false ha nem

	Wrapper függvény a v_donatorship resource-hoz.
]]
function isPlayerDonator(player)
	return exports.v_donatorship:isPlayerDonator(player)
end

--[[
	MYSQL WRAPPER FÜGGVÉNYEK

	Ezek a függvények egyszerűsítik a MySQL műveletek hívását
	más resource-okból.
]]

-- Játékos statisztika beállítása
setPlayerStats = function(player, stats, value, ...)
	return exports.v_mysql:setPlayerStats(player, stats, value, ...)
end

-- Játékos statisztika lekérése
getPlayerStats = function(player, stats, ...)
	return exports.v_mysql:getPlayerStats(player, stats, ...)
end

-- Játékos statisztikából levonás
takePlayerStats = function(player, stats, ...)
	return exports.v_mysql:takePlayerStats(player, stats, ...)
end