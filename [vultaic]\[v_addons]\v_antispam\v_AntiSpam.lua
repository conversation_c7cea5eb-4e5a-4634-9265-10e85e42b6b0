--[[
	VULTAIC MGM - ANTISPAM ADDON

	Ez a fájl az antispam (spam elleni védelem) addon logikáját tartalmazza.

	Főbb funkciók:
	- Parancs spam védelem
	- Időalapú figyelmeztetési rendszer
	- Gameplay parancsok kivétele
	- Automatikus blokkolás

	Működés:
	1. Parancs végrehajtás figyelése
	2. Időintervallum ellenőrzése (1 másodperc)
	3. Figyelmeztetések számolása
	4. 3 figyelmeztetés után blokkolás

	Kivételek:
	- Previous: Előző map parancs
	- Next: Következő map parancs
]]

-- Parancs spam nyilvántartás (játékos -> {warn, tick})
local commandSpam = {}

-- Gameplay parancsok amelyek nem számítanak spam-nek
local gameplayCommands = {
	Previous = true,                    -- Előző map parancs
	Next = true                         -- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> map parancs
}

--[[
	Parancs spam védelem

	@param string cmd - Végrehajtott parancs neve

	Ez a függvény minden parancs végrehajtásakor lefut és ellenőrzi:
	1. A parancs gameplay parancs-e (kivétel)
	2. Az utolsó parancs óta eltelt időt
	3. A figyelmeztetések számát
	4. Szükség esetén blokkolja a parancsot
]]
function preventCommandSpam(cmd)
	-- Gameplay parancsok kihagyása
	if gameplayCommands[cmd] then
		return
	end

	-- Játékos spam adatainak inicializálása
	if(not commandSpam[source]) then
		commandSpam[source] = {warn = 1, tick = getTickCount()}
	end

	-- Időadatok lekérése
	local lastTick = commandSpam[source].tick
	local warns = commandSpam[source].warn
	local now = getTickCount()

	-- Spam ellenőrzés (1 másodpercen belül)
	if(now - lastTick < 1000) then
		commandSpam[source].warn = warns+1   -- Figyelmeztetés növelése
	else
		commandSpam[source].tick = now        -- Idő frissítése
		commandSpam[source].warn = 1          -- Figyelmeztetések nullázása
	end

	-- Spam blokkolás (3 figyelmeztetés után)
	if commandSpam[source].warn > 3 then
		commandSpam[source].tick = now
		cancelEvent()                         -- Parancs blokkolása
		outputChatBox("ERROR :: #ffffffPlease refrain from spamming!", source, 255, 0, 0, true)
	end
end
addEventHandler("onPlayerCommand", root, preventCommandSpam)