-- <PERSON><PERSON><PERSON> létrehozása a Vultaic MGM rendszerhez
-- Futtasd: mysql -u sunuser -p < create_clan_tables.sql

-- <PERSON><PERSON> adatb<PERSON>zis
USE v_accounts;

-- <PERSON><PERSON><PERSON><PERSON> tábla
CREATE TABLE IF NOT EXISTS v_clans (
    clan_id INT PRIMARY KEY AUTO_INCREMENT,
    clan_name VARCHAR(32) NOT NULL UNIQUE,
    clan_tag VARCHAR(8) NOT NULL UNIQUE,
    leader_id INT NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (leader_id) REFERENCES vmtasa_accounts(account_id),
    INDEX idx_name (clan_name),
    INDEX idx_tag (clan_tag)
);

-- <PERSON><PERSON><PERSON> tagság tábla
CREATE TABLE IF NOT EXISTS v_clan_members (
    id INT PRIMARY KEY AUTO_INCREMENT,
    clan_id INT NOT NULL,
    account_id INT NOT NULL,
    rank <PERSON><PERSON><PERSON>('member', 'moderator', 'admin', 'leader') DEFAULT 'member',
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIG<PERSON> KEY (clan_id) REFERENCES v_clans(clan_id) ON DELETE CASCADE,
    FOREIGN KEY (account_id) REFERENCES vmtasa_accounts(account_id) ON DELETE CASCADE,
    UNIQUE KEY unique_membership (clan_id, account_id)
);

-- Fejlesztői adatbázis
USE v_accounts_dev;

-- Klánok tábla
CREATE TABLE IF NOT EXISTS v_clans (
    clan_id INT PRIMARY KEY AUTO_INCREMENT,
    clan_name VARCHAR(32) NOT NULL UNIQUE,
    clan_tag VARCHAR(8) NOT NULL UNIQUE,
    leader_id INT NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (leader_id) REFERENCES vmtasa_accounts(account_id),
    INDEX idx_name (clan_name),
    INDEX idx_tag (clan_tag)
);

-- Klán tagság tábla
CREATE TABLE IF NOT EXISTS v_clan_members (
    id INT PRIMARY KEY AUTO_INCREMENT,
    clan_id INT NOT NULL,
    account_id INT NOT NULL,
    rank ENUM('member', 'moderator', 'admin', 'leader') DEFAULT 'member',
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (clan_id) REFERENCES v_clans(clan_id) ON DELETE CASCADE,
    FOREIGN KEY (account_id) REFERENCES vmtasa_accounts(account_id) ON DELETE CASCADE,
    UNIQUE KEY unique_membership (clan_id, account_id)
);

-- Ellenőrzés
SELECT 'v_accounts klán táblák:' as info;
USE v_accounts;
SHOW TABLES LIKE 'v_clan%';

SELECT 'v_accounts_dev klán táblák:' as info;
USE v_accounts_dev;
SHOW TABLES LIKE 'v_clan%';

SELECT 'Klán táblák sikeresen létrehozva!' as result;
