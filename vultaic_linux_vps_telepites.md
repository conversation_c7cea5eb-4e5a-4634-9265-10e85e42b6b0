# Vultaic MGM szerver telepítése Linux VPS-re – Lépésről lépésre útmutató

Ez az útmutató végigvezet a Vultaic MGM szerver teljes telepítésén egy Linux VPS-re, az adatbázis és táblák létrehozásától a weboldal/fórum beállításáig. 

---

## 1. <PERSON><PERSON>k<PERSON>
- **Linux VPS szükséges** (pl. Ubuntu 20.04/22.04 ajánlott)
- **SSH hozzáférés** a VPS-hez
- **Domain vagy IP** a weboldalhoz/fórumhoz (opcionális, de ajánlott)

---

## 2. Szükséges csomagok telepítése
```bash
sudo apt update && sudo apt upgrade -y
sudo apt install git build-essential libncurses5 libreadline-dev libssl-dev libxml2-dev libsqlite3-dev zlib1g-dev libcurl4-openssl-dev
sudo apt install mariadb-server mariadb-client
sudo apt install nginx php php-fpm php-mysql unzip
```

---

## 3. MTA szerver telepítése
1. **Letöltés:**
   - https://mtasa.com/download/ – válaszd a Linux szerver verziót!
2. **Kicsomagolás:**
   ```bash
   tar -xvf mta-server*.tar.gz
   mv mta-server* /opt/mta
   cd /opt/mta
   ```
3. **Első indítás:**
   ```bash
   ./mta-server64
   ```
   (Első indítás után leállíthatod, hogy betöltsd a resource-okat.)

---

## 4. Vultaic MGM resource-ok feltöltése
1. **Git klónozás vagy feltöltés:**
   - Másold vagy klónozd a Vultaic MGM resource-okat az `/opt/mta/mods/deathmatch/resources/` könyvtárba.
2. **ACL, config, szerver.cfg beállítás:**
   - Állítsd be az adminokat, resource-okat, portokat a `server.cfg`-ben.
   - Ellenőrizd a `v_mysql` configot (adatbázis elérés).

---

## 5. MySQL adatbázis és táblák létrehozása
1. **MySQL/MariaDB indítása:**
   ```bash
   sudo systemctl start mariadb
   sudo mysql_secure_installation
   ```
2. **Adatbázis létrehozása:**
   ```sql
   CREATE DATABASE v_accounts CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   CREATE USER 'mta'@'localhost' IDENTIFIED BY 'erősjelszó';
   GRANT ALL PRIVILEGES ON v_accounts.* TO 'mta'@'localhost';
   FLUSH PRIVILEGES;
   ```
3. **Táblák létrehozása (példa):**
   ```sql
   USE v_accounts;
   CREATE TABLE vmtasa_accounts (
     account_id INT PRIMARY KEY AUTO_INCREMENT,
     username VARCHAR(32) NOT NULL,
     password_hash VARCHAR(128) NOT NULL,
     money INT DEFAULT 0,
     dm_points INT DEFAULT 0,
     os_points INT DEFAULT 0,
     dd_points INT DEFAULT 0,
     race_points INT DEFAULT 0,
     shooter_points INT DEFAULT 0,
     hunter_points INT DEFAULT 0,
     tdm_points INT DEFAULT 0,
     Clan VARCHAR(32),
     data JSON,
     tuning JSON,
     awards JSON,
     regdate TIMESTAMP DEFAULT CURRENT_TIMESTAMP
   );
   ```
   (További táblákhoz lásd: vultaic_db_map.md)

---

## 6. Szerver indítása
```bash
cd /opt/mta
./mta-server64
```
- Ellenőrizd a konzolban, hogy minden resource elindul-e, nincs-e hibaüzenet.

---

## 7. Weboldal/fórum telepítése (IPB vagy phpBB példa)
### IPB (Invision Power Board)
1. **Letöltés:**
   - IPB licenc szükséges, vagy válassz ingyenes alternatívát (pl. phpBB).
2. **Feltöltés:**
   - Töltsd fel a web rootba (pl. `/var/www/html/forum`).
3. **Telepítés:**
   - Böngészőben nyisd meg a domain/IP cím + `/forum` útvonalat, kövesd a telepítőt.
   - Add meg az adatbázis elérési adatokat (külön DB javasolt: `v_forum`).
4. **Jogosultságok, admin fiók létrehozása.**

### phpBB (ingyenes)
1. **Letöltés:**
   - https://www.phpbb.com/downloads/
2. **Kicsomagolás, feltöltés:**
   ```bash
   unzip phpBB-*.zip
   mv phpBB3 /var/www/html/forum
   ```
3. **Telepítés:**
   - Böngészőben a `/forum/install` oldalon kövesd a telepítőt.
   - Add meg az adatbázis elérési adatokat.

---

## 8. Fórum/szerver regisztráció és statisztika integráció
- A Vultaic rendszer támogatja a fórum alapú regisztrációt (IPB vagy phpBB), ehhez a `v_mysql` vagy `ipb` resource-ban kell beállítani a fórum DB elérési adatokat.
- A statisztikák webes megjelenítéséhez készíthetsz egy egyszerű PHP/Python oldalt, amely a `vmtasa_accounts` táblából olvas adatokat (pl. toplista, játékosprofilok).
- A regisztráció történhet a fórumon, majd a szerver szinkronizálja a fórum accountokat (részletek: vultaic_db_map.md, ipb resource dokumentáció).

---

## 9. Hasznos tanácsok
- Mindig használj erős jelszavakat!
- Az adatbázis elérési adatokat ne tárold hardcoded módon, hanem külön configban.
- Rendszeresen készíts biztonsági mentést az adatbázisról és a szerver resource-okról!
- A szervert tűzfal mögött futtasd, csak a szükséges portokat nyisd meg (pl. 22003, 22005, 22006, 80, 443).

---

## 10. Hibakeresés, támogatás
- Ellenőrizd a szerver konzolt és a resource logokat hiba esetén.
- A weboldal/fórum telepítéséhez részletes leírásokat találsz az adott rendszer hivatalos oldalán.
- További kérdés esetén írj, vagy keresd fel az MTA/Vultaic közösséget!

---

*Ez az útmutató lépésről lépésre segít a Vultaic MGM szerver, adatbázis és weboldal/fórum telepítésében egy Linux VPS-en. Ha bármelyik lépésnél elakadsz, vagy részletesebb SQL sémára, integrációs példára van szükséged, jelezd bátran!*
