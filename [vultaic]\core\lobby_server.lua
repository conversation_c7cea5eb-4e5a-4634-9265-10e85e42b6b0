--[[
	VULTAIC MGM - LOBBY SZERVER OLDAL

	Ez a fájl a lobby (váróterem) rendszer szerver oldali funkcióit tartalmazza.
	A lobby az a hely, ahol a játékosok tartózkodnak, amikor nem vesznek részt
	aktív játékban. Innen tudnak arenákat választani és csatlakozni.

	Főbb funkciók:
	- Lobby element létrehozása és kezelése
	- Játékosok áthelyezése a lobbiba
	- Resource indítás kezelése
	- Kamera pozíció beállítása
]]

-- Lobby element létrehozása - ez reprezentálja a várótermet
local lobbyElement = createElement("arena", "lobby")
-- Lobby dimenzió (0 = alapértelmezett dimenzió)
local lobbyDimension = 0

--[[
	Játékos áthelyezése a lobbiba

	@param element player - A játékos element

	Ez a függvény áthelyezi a játékost a lobbiba:
	- Beállítja a lobby elementet szülőként
	- Átállítja a dimenziót 0-ra
	- Spawn-olja a játékost a lobby pozícióra
	- Befagyasztja a játékost
	- Beállítja a kamera pozíciót
	- Értesíti a többi játékost az arena elhagyásról
]]
function movePlayerToLobby(player)
	if isElement(player) then
		local arena = getElementParent(player)
		-- Játékos áthelyezése a lobby element alá
		setElementParent(player, lobbyElement)
		-- Dimenzió beállítása lobby-ra (0)
		setElementDimension(player, lobbyDimension)
		-- Arena adat beállítása "lobby"-ra
		setElementData(player, "arena", "lobby")
		-- Játékos spawn-olása a lobby pozícióra (0, 0, 5)
		spawnPlayer(player, 0, 0, 5)
		-- Játékos befagyasztása (nem tud mozogni)
		setElementFrozen(player, true)
		-- Kamera pozíció beállítása - lobby nézet (felülnézet)
		setCameraMatrix(player, 554.77648925781, -1614.8415527344, 30.357982635498, 490.26504516602, -1538.7777099609, 37.608291625977)
		-- Értesítés küldése az elhagyott arenának (ha volt)
		if isElement(arena) and arena ~= root and arena ~= lobbyElement then
			triggerClientEvent(arena, "notification:create", arena, "Arena", getPlayerName(player).." #FFFFFFelhagyta az arenát", "joinquit", "leave")
		end
	end
end

--[[
	Resource indítás kezelése

	Amikor a core resource elindul:
	- Beállítja a szerver játéktípusát
	- Beállítja a lobby nevét
	- Minden játékost áthelyez a lobbiba
	- Kioszt ID-kat a játékosoknak
]]
addEventHandler("onResourceStart", resourceRoot,
function()
	-- Szerver játéktípus beállítása (ez jelenik meg a szerver böngészőben)
	setGameType("AURORAMTA.COM")
	-- Lobby element nevének beállítása
	setElementData(lobbyElement, "name", "Lobby")

	-- Minden jelenlegi játékos áthelyezése a lobbiba
	for i, player in pairs(getElementsByType("player")) do
		local arena = getElementData(source, "arena")
		-- Ha a játékos arenában volt, eltávolítjuk onnan
		if arena then
			removePlayerFromArena(player, arena)
		end
		-- Áthelyezés a lobbiba
		movePlayerToLobby(player)
		-- Egyedi ID kiosztása
		assignPlayerID(player)
	end
end)