--[[
	VULTAIC MGM - BIZTONSÁGI RENDSZER SZERVER OLDAL

	Ez a fájl a szerver biztonsági rendszerét tartalmazza:
	- Kapcsola<PERSON> biztonság (serial ellenőrzés)
	- Nickname validáció és egyediség
	- Random név generálás
	- Resource kompilálás
	- Parancs naplózás

	Főbb biztonsági funkciók:
	- Serial nélküli kapcsolatok blokkolása
	- Minimum 3 karakteres nevek kikényszerítése
	- Duplikált nevek megakadályozása
	- Automatikus random név generálás
	- Admin parancsok naplózása
]]

--[[
	BIZTONSÁGOS KAPCSOLATOK KEZELÉSE

	Ez az event kezelő ellenőrzi, hogy a csatlakozó játékosnak van-e serial száma.
	A serial szám egyedi azonosító minden MTA kliens számára.
]]
addEventHandler("onPlayerConnect", root,
function(nick, ip, username, serial, versionNumber)
	if not serial then
		outputDebugString("Security: "..nick.." has tried to connect without a serial number")
		outputServerLog("Security: "..nick.." has tried to connect without a serial number")
		cancelEvent(true, "You are not allowed to play here without a serial")
	end
end)

--[[
	RANDOM AZONOSÍTÓ RENDSZER

	Ez a rendszer random neveket generál azoknak a játékosoknak,
	akiknek a neve túl rövid vagy nem megfelelő.
]]
local randomIDs = {}        -- Használt random ID-k tárolása
local playerRandomID = {}   -- Játékos -> Random ID mapping

--[[
	Szabad random ID keresése

	@return number - Szabad ID szám (100-tól kezdve)
]]
function getRandomID()
	local id = 100
	while randomIDs[id] ~= nil do
		id = id + 1
	end
	return id
end

--[[
	Játékos csatlakozás kezelése

	Ellenőrzi a játékos nevét és szükség esetén random nevet ad.
	Minimum 3 karakter szükséges a névhez (színkódok nélkül).
]]
addEventHandler("onPlayerJoin", root,
function()
	-- Név tisztítása színkódoktól
	local clearName = getPlayerName(source):gsub("#%x%x%x%x%x%x", "")

	-- Ha a név túl rövid, random nevet generálunk
	if #clearName < 3 then
		local randomID = getRandomID()
		setPlayerName(source, "RandomDude"..randomID)
		randomIDs[randomID] = true
		playerRandomID[source] = randomID
	end
end)

--[[
	Játékos kilépés kezelése

	Felszabadítja a játékos által használt random ID-t.
]]
addEventHandler("onPlayerQuit", root,
function()
	local randomID = playerRandomID[source]
	if randomID then
		if randomIDs[randomID] then
			randomIDs[randomID] = nil
		end
		playerRandomID[source] = nil
	end
end)

addEventHandler("onPlayerChangeNick", root,
function(oldNick, newNick)
	local clearName = newNick:gsub("#%x%x%x%x%x%x", "")
	if #clearName < 3 then
		cancelEvent()
		outputChatBox("Your nickname must contain mimimum 3 characters.", source, 255, 0, 0, true)
		return
	end

	-- Hatékonyabb ellenőrzés a beépített MTA függvénnyel
	local existingPlayer = getPlayerFromName(newNick)
	if existingPlayer and existingPlayer ~= source then
		cancelEvent()
		outputChatBox("This nickname is already in use.", source, 255, 0, 0, true)
	end
end)

addCommandHandler("compileres",
function(player, command, resourceName)
	if not hasObjectPermissionTo(player, "function.startResource", false) or not resourceName then
		return
	end
	local metaFile = xmlLoadFile(":"..resourceName.."/meta.xml")
	if metaFile then
		outputChatBox("Compiling resource: "..resourceName, root, 255, 255, 0)
		for i, node in pairs (xmlNodeGetChildren(metaFile)) do
			local info = xmlNodeGetAttributes(node)
			if xmlNodeGetName(node) == "script" and info["type"] == "client" then
				local file = fileOpen(":"..resourceName.."/"..info["src"], true)
				if file then
					-- FIGYELEM: Ez a szolgáltatás nem biztonságos, mert a kódot egy külső félhez küldi.
					fetchRemote("https://luac.mtasa.com/?compile=1&debug=0&obfuscate=2", -- HTTPS-re cserélve a titkosított átvitelért.
						function(data)
							if fileExists(savePath) then
								fileDelete(savePath)
							end
							local compiledFile = fileCreate(savePath)
							if compiledFile then
								fileWrite(compiledFile, data)
								fileFlush(compiledFile)
								fileClose(compiledFile)
							end
					end, fileRead(file, fileGetSize(file)) , true )
					fileClose(file)
				end
			end
		end
		xmlUnloadFile(metaFile)
	end
end)

addEventHandler("onPlayerCommand", root,
function(command)
	outputServerLog(getPlayerName(source):gsub("#%x%x%x%x%x%x", "").." has just tried to use command: '"..command.."'")
	--[[ -- IDEIGLENESEN KIKOMMENTELVE A DEBUGGOLÁSHOZ
	-- Ez a rész megakadályozza, hogy nem-adminok használják a debugscript parancsot.
	if command == "debugscript" and not hasObjectPermissionTo(source, "function.kickPlayer", false) then
		cancelEvent()
		return
	end
	--]]
end)

-- A redundáns onPlayerChangeNick event kezelő eltávolítva és bevonva a fenti blokkba.