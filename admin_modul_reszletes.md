# Vultaic [admin] könyvtár – R<PERSON>zletes leírás

## Tartalomjegyzék
- [<PERSON><PERSON><PERSON><PERSON><PERSON>](#áttekintés)
- [Fő almappák és szerepük](#fő-almappák-és-szerepük)
  - [admin](#admin)
  - [admin2](#admin2)
  - [v_admin](#v_admin)
  - [ipb](#ipb)
  - [runcode](#runcode)
  - [acpanel](#acpanel)
- [Miért több admin modul?](#miért-több-admin-modul)
- [Összefoglalás](#összefoglalás)

---

## Áttekintés
A Vultaic rendszer `[admin]` könyvtára a szerver adminisztrációs, jogosultságkezelési és fejlesztői funkcióit gyűjti össze. Minden fontosabb adminisztrációs modul külön almappában tal<PERSON>, ami a <PERSON>, karbantarthatós<PERSON>got és bővíthetőséget szolgálja.

---

## Fő almappák és szerepük

### admin
- **<PERSON><PERSON>, klasszikus MTA admin resource** (alap adminpanel).
- Fő funkciók: játékos menedzsment (kick, ban, mute), szerver státusz, alapjogosultságok kezelése, játékosinformációk, chatlog, stb.
- GUI alapú, minden admin számára elérhető, jól dokumentált és stabil.
- Kiegészítő vagy fallback szerep a Vultaicban.

### admin2
- **Kibővített, fejlettebb admin rendszer.**
- Részletesebb jogosultságkezelés, fejlettebb GUI, finomhangolt jogkiosztás, fejlettebb naplózás.
- A Vultaic rendszer fő adminisztrációs felülete, modernebb, jobban illeszkedik a szerver igényeihez.
- Fejlesztői/debug funkciókat is tartalmazhat.

### v_admin
- **Speciális, Vultaic-hoz igazított admin funkciók.**
- Egyedi adminisztrációs vagy fejlesztői eszközök (pl. aréna adminisztráció, Vultaic-specifikus parancsok).
- Olyan funkciók, amelyek nem részei a klasszikus admin resource-oknak.

### ipb
- **Invision Power Board (IPB) fórum integráció.**
- A szerver és a központi fórum közötti kommunikáció (jogosultságok, account státuszok, fórumról átvett admin rangok).
- API-hívások, PHP endpointok, hitelesítési és jogosultság-szinkronizációs logika.
- Az admin jogokat a fórumon keresztül is lehet kezelni.

### runcode
- **Futtatható kód (remote code execution) modul.**
- Fejlesztői/debug eszköz, amivel szerveroldali Lua kódot lehet futtatni élőben.
- Csak magas jogosultságú fejlesztők számára elérhető!
- Hasznos hibakereséshez, de szigorú logolás, audit és jogosultságkezelés szükséges.

### acpanel
- **Anti-Cheat Control Panel.**
- UI az anti-cheat beállításokhoz, státuszokhoz, logokhoz.
- Segíti az adminokat az AC modulok kezelésében, ki-/bekapcsolásában, információk megjelenítésében.

---

## Miért több admin modul?

- **Kompatibilitás:** A klasszikus `admin` resource minden szerveren működik, de kevésbé bővíthető.
- **Modernitás, bővíthetőség:** Az `admin2` fejlettebb, jobban testreszabható, ezért a Vultaic rendszer fő adminpanelje.
- **Speciális igények:** A `v_admin` csak Vultaic-specifikus funkciókat tartalmaz.
- **Központi jogosultságkezelés:** Az `ipb` integrációval a fórumon kiosztott jogok automatikusan érvényesülnek a szerveren.
- **Fejlesztői eszközök:** A `runcode` lehetővé teszi a dinamikus szerveroldali fejlesztést, de csak szigorú kontroll mellett.

---

## Összefoglalás
A `[admin]` könyvtár szerkezete a modularitást, biztonságot és a fejlett adminisztrációs lehetőségeket szolgálja. Az egyes almappák jól elkülönítik a klasszikus, fejlett, Vultaic-specifikus, fórum-alapú és fejlesztői admin funkciókat, így a rendszer egyszerre marad kompatibilis, bővíthető és biztonságos.

Ha szeretnéd, bármelyik almappát vagy kulcsfontosságú scriptet részletesen is bemutatok, vagy elmagyarázom a jogosultságkezelés, integráció vagy fejlesztői workflow működését!
