--[[
	VULTAIC MGM - GARAGE RENDSZER SZERVER OLDAL

	Ez a fájl a garage (garázs) rendszer szerver oldali logikáját tartalmazza.

	Főbb funkciók:
	- Garage arena regisztrálása
	- Garage pálya betöltése és kezelése
	- Játékos garage-ba mozgatása
	- Játékos garage-ból eltávolítása
	- Kliens-szerver kommunikáció
	- Mapmanager integráció

	Működés:
	1. Arena regisztrálása a core rendszerben
	2. Garage pálya betöltése (vultaic-garage-map-new)
	3. Játékosok kezelése (belépés/kilépés)
	4. Kliens oldali eventek triggerelése

	Beállítások:
	- Név: "Garage"
	- ID: "garage"
	- Bejelentkezés szükséges: igen
]]

-- Garage arena beállítások
local settings = {
	name = "Garage",        -- Arena megjelenített neve
	id = "garage",          -- Arena egyedi azonosítója
	requireLogin = true     -- Bejelentkezés szükséges-e
}

-- Garage objektum adatok tárolása
local garage = {}

-- Event definíció a mapmanager integrációhoz
addEvent("mapmanager:onPlayerLoadMap", true)

--[[
	RESOURCE INDÍTÁS KEZELÉSE

	Arena regisztrálása és garage pálya betöltése.
]]
addEventHandler("onResourceStart", resourceRoot,
function()
	-- Arena regisztrálása a core rendszerben
	if not exports.core or not exports.core.registerArena then
		outputServerLog("[v_garage] Critical Error: 'core' resource or 'registerArena' export is not available.")
		return
	end
	local data = exports.core:registerArena(settings)
	if not data then
		outputServerLog("[v_garage] Failed to register arena with core. Check ACL and resource start order.")
		return
	end

	-- Garage adatok mentése
	garage.element = data.element      -- Arena element
	garage.dimension = data.dimension  -- Arena dimenzió

	if not exports.mapmanager or not exports.mapmanager.loadMapData then
		outputServerLog("[v_garage] Critical Error: 'mapmanager' resource or 'loadMapData' export is not available.")
		return
	end
	-- Garage pálya betöltése
	garage.map = exports.mapmanager:loadMapData(garage.element, "[garage_map]/vultaic-garage-map-new")

	-- Event kezelő hozzáadása a pálya betöltéshez
	addEventHandler("mapmanager:onPlayerLoadMap", garage.element, handlePlayerLoad)

	-- Ellenőrzés, hogy sikerült-e a pálya betöltése
	if not garage.map then
		outputServerLog("[v_garage] ERROR: Failed to load garage map! Check if the map resource '[garage_map]/vultaic-garage-map-new' exists and is correct.")
	end
end)

--[[
	RESOURCE LEÁLLÍTÁS KEZELÉSE

	Garage pálya eltávolítása a memóriából.
]]
addEventHandler("onResourceStop", resourceRoot,
function()
	if exports.mapmanager and garage.map and garage.map.info then
		exports.mapmanager:unloadMapData(garage.element, garage.map.info.resourceName)
	end
end)

--[[
	Játékos garage-ba mozgatása

	@param element player - A játékos element

	Ez a függvény küldi el a garage pálya adatait a játékosnak.
]]
function movePlayerToArena(player)
	exports.mapmanager:sendMapData(garage.element, garage.map.info.resourceName, {player})
end

--[[
	Játékos eltávolítása a garage-ból

	@param element player - A játékos element

	Kliens oldali garage kilépés event triggerelése.
]]
function removePlayerFromArena(player)
	triggerClientEvent(player, "garage:onClientLeaveGarage", resourceRoot)
end

--[[
	Játékos pálya betöltés kezelése

	Amikor a játékos betöltötte a garage pályát,
	triggerel egy kliens oldali eventet.
]]
function handlePlayerLoad()
	triggerClientEvent(source, "garage:onClientJoinGarage", resourceRoot)
end