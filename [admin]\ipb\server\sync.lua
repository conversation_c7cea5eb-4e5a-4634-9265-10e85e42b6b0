
--[[
	VULTAIC MGM - IPB (FORUM INTEGRÁCIÓ) SYNC SZERVER OLDAL

	Ez a fájl az IPB (Invision Power Board) forum integráció szinkronizációs
	logikáját tartalmazza.

	Főbb funkciók:
	- <PERSON>zerver statisztika figyelés
	- Performance monitoring
	- Valós idejű adatok küldése
	- Listener kezel<PERSON> (ki figyeli a statokat)
	- Kategória alapú szűrés
	- Jogosultság ellenőrzés

	Listener rendszer:
	- Weak reference táblázat (automatikus cleanup)
	- Kategória alapú szűrés
	- Opciók és filter beállítások
	- Valós idejű frissítés
]]

-- Listener táblázat weak reference-szel (automatikus cleanup)
g_Listener = setmetatable({}, {__mode = "k"})

--[[
	Listener hozzáadása egy játékoshoz

	@param element player - A figy<PERSON><PERSON> játé<PERSON>
]]
function addListener(player)
    if not g_Listener[player] then
        g_Listener[player] = {
            category = "Server info",               -- Alapértelmezett kategória
            options = "",                           -- Opciók string
            filter = ""                             -- Szűrő string
        }
    end
end

--[[
	Listener eltávolítása

	@param element player - A játékos
]]
function removeListener(player)
    g_Listener[player] = nil
end

--[[
	Ellenőrzi, hogy a játékos figyeli-e a statokat

	@param element player - A játékos
	@return boolean - Figyeli-e
]]
function isListening(player)
    return g_Listener[player] and true
end

--[[
	Statisztikák küldése a figyelő játékosnak

	@param element player - A játékos
	@param string mode - Frissítési mód
]]
function sendListenerStats(player, mode)
    local settings = g_Listener[player]

    if settings then
        -- Performance statisztikák lekérése és küldése
        player:triggerEvent("ipb.updateStats", player, mode, getPerformanceStats(settings.category, settings.options, settings.filter))
    end
end

--[[
	JÁTÉKOS KILÉPÉS KEZELÉSE

	Automatikusan eltávolítja a listener-t kilépéskor.
]]
addEventHandler("onPlayerQuit", root,
    function ()
        removeListener(source)
    end
)

--[[
	IPB TOGGLE EVENT KEZELÉSE

	Kliens oldali kérésre be/kikapcsolja a statisztika figyelést.
]]
addEvent("ipb.toggle", true)
addEventHandler("ipb.toggle", root,
    function (enabled, category)
        -- Jogosultság és engedélyezés ellenőrzése
        if enabled and client:hasIPBAccess() then
            addListener(client)

            -- Kategória beállítása (ha megadva)
            if category then
                g_Listener[client].category = category
            end

            -- Kezdeti statisztikák küldése
            sendListenerStats(client, STATS_MODE_NEW_LISTENER)
        else
            -- Listener eltávolítása
            removeListener(client)
        end
    end
)

addEvent("ipb.updateCategory", true)
addEventHandler("ipb.updateCategory", root,
    function (category)
        if not isListening(client) then
            return
        end

        g_Listener[client].category = category
        sendListenerStats(client, STATS_MODE_CATEGORY_CHANGE)
    end
)

addEvent("ipb.updateOptions", true)
addEventHandler("ipb.updateOptions", root,
    function (options)
        if not isListening(client) then
            return
        end

        g_Listener[client].options = options
        sendListenerStats(client, STATS_MODE_OPTIONS_CHANGE)
    end
)

addEvent("ipb.updateFilter", true)
addEventHandler("ipb.updateFilter", root,
    function (filter)
        if not isListening(client) then
            return
        end

        g_Listener[client].filter = filter
        sendListenerStats(client, STATS_MODE_FILTER_CHANGE)
    end
)

function updateListeners()
    for player in pairs(g_Listener) do
        if isElement(player) then
            sendListenerStats(player, STATS_MODE_REFRESH)
        else
            g_Listener[player] = nil
        end
    end
end
Timer(updateListeners, UPDATE_FREQUENCY, 0)
