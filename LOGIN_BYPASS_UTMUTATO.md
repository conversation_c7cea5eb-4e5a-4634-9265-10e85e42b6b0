# Login Bypass Útmutató - Panel Teszteléshez

## 🎯 C<PERSON>l
Ideiglenes megoldás a panel kinézetének teszteléséhez külső adatbázis kapcsolat nélkül.

## ⚠️ FIGYELEM
**Ez a megoldás CSAK fejlesztési/tesztelési célokra szolgál!**
- NE használd éles környezetben
- A bypass mód biztonsági kockázatot jelent
- Tesztelés után állítsd vissza az eredeti login rendszert

---

## 🔧 Beállítás

### 1. Fájlok Ellenőrzése
A következő fájlokat hoztam létre/módosítottam:

✅ **Új fájl:** `[vultaic]/v_login/login_server_bypass.lua`
✅ **Módosított:** `[vultaic]/v_login/meta.xml`

### 2. Szerver Újraindítás
A változtatások érvénybe lépéséhez:
```bash
# MTA szerver konzolban:
restart v_login
# vagy
restart core
```

---

## 👤 Teszt Felhasználók

### Alapfelhasználó
- **Username:** `test`
- **Password:** `test`
- **Szint:** 25
- **Pénz:** 50,000
- **Pontok:** 1,000
- **Klán:** TestClan (Leader)

### Admin Felhasználó
- **Username:** `admin`
- **Password:** `admin`
- **Szint:** 99
- **Admin Level:** 5 (Tulajdonos)
- **Pénz:** 999,999
- **Pontok:** 99,999
- **Klán:** AdminClan (Owner)

### Kezdő Játékos
- **Username:** `player`
- **Password:** `player`
- **Szint:** 5
- **Pénz:** 10,000
- **Pontok:** 250
- **Klán:** Nincs

---

## 🎮 Használat

### 1. Belépés
1. Indítsd el a szervert
2. Csatlakozz a szerverre
3. A login képernyőn használd a fenti felhasználóneveket és jelszavakat
4. Sikeres belépés esetén megjelenik: "Successfully logged in as [username] (BYPASS MODE)"

### 2. Panel Tesztelése
A belépés után elérhető lesz:
- **F1** - Fő panel (v_panel)
- **F2** - Chat
- **F3** - Scoreboard
- Minden egyéb UI elem

### 3. Debug Parancsok
```bash
# Elérhető felhasználók listája
/bypass_info

# Bypass mód ki/bekapcsolása (csak adminoknak)
/toggle_bypass
```

---

## 🔍 Tesztelési Területek

### Panel Funkciók
- ✅ **Statisztikák tab** - Pénz, pontok, szint megjelenítése
- ✅ **Klán tab** - Klán információk
- ✅ **Eredmények tab** - Wins/kills/deaths
- ✅ **Beállítások tab** - Felhasználói beállítások
- ✅ **Admin panel** - Admin funkcionalitás (admin userrel)

### UI Elemek
- ✅ **Avatar megjelenítés**
- ✅ **Pénz/pont számlálók**
- ✅ **Progress barok**
- ✅ **Táblázatok**
- ✅ **Gombok és menük**

---

## 🐛 Hibaelhárítás

### Probléma: Nem tudok belépni
**Megoldás:**
1. Ellenőrizd, hogy a szerver újraindult-e
2. Konzolban nézd meg: "LOGIN BYPASS MODE ACTIVE" üzenet
3. Használd pontosan a megadott felhasználóneveket/jelszavakat

### Probléma: Panel nem jelenik meg
**Megoldás:**
1. Ellenőrizd, hogy a v_panel resource fut-e
2. Nézd meg a debug konzolt hibákért
3. Próbálj F1-gyel megnyitni a panelt

### Probléma: Hiányzó adatok a panelben
**Megoldás:**
1. A dummy adatok a `login_server_bypass.lua` fájlban módosíthatók
2. Adj hozzá további mezőket a `dummyUsers` táblázathoz

---

## 🔄 Visszaállítás Eredeti Állapotra

### 1. Meta.xml Visszaállítása
```xml
<meta>
    <min_mta_version client="1.5.3-9.11270" server="1.5.3-9.11270"/>
    <!-- Eredeti login server visszaállítása -->
    <script src="login_server.lua" type="server"/>
    <!-- Bypass verzió kikapcsolása -->
    <!-- <script src="login_server_bypass.lua" type="server"/> -->
    ...
</meta>
```

### 2. Szerver Újraindítás
```bash
restart v_login
```

### 3. Bypass Fájl Törlése (opcionális)
```bash
# A login_server_bypass.lua fájl törölhető
```

---

## 📝 Dummy Adatok Testreszabása

Ha más adatokra van szükséged a teszteléshez, módosítsd a `login_server_bypass.lua` fájlban a `dummyUsers` táblázatot:

```lua
["sajat_user"] = {
    password = "sajat_jelszo",
    connect_id = 4,
    username = "sajat_user",
    user_id = 4,
    money = 75000,        -- Pénz
    points = 2500,        -- Pontok
    level = 50,           -- Szint
    playtime = 1800000,   -- Játékidő (ms)
    wins = 200,           -- Győzelmek
    kills = 800,          -- Ölések
    deaths = 150,         -- Halálok
    clan_id = 2,          -- Klán ID
    clan_name = "MyTestClan",  -- Klán név
    clan_rank = "Member",      -- Klán rang
    admin_level = 2,      -- Admin szint (opcionális)
    -- További mezők...
}
```

---

## 🎨 Panel Kinézet Tesztelési Checklist

### ✅ Alapvető Megjelenítés
- [ ] Panel megnyílik F1-re
- [ ] Tabok válthatók
- [ ] Szövegek helyesen jelennek meg
- [ ] Ikonok és képek betöltődnek

### ✅ Adatok Megjelenítése
- [ ] Felhasználónév megjelenik
- [ ] Pénz összeg formázva
- [ ] Pontok helyesen
- [ ] Szint progress bar
- [ ] Játékidő formázva

### ✅ Klán Funkciók
- [ ] Klán név megjelenik
- [ ] Klán rang helyesen
- [ ] Klán statisztikák

### ✅ Admin Funkciók (admin userrel)
- [ ] Admin panel elérhető
- [ ] Admin parancsok működnek
- [ ] Jogosultság ellenőrzések

### ✅ Responsive Design
- [ ] Különböző felbontásokon
- [ ] Ablak átméretezéskor
- [ ] Hosszú szövegek kezelése

---

## 📞 Támogatás

Ha problémába ütközöl:
1. Nézd meg a szerver konzolt hibákért
2. Ellenőrizd a debug üzeneteket
3. Használd a `/bypass_info` parancsot
4. Módosítsd a dummy adatokat szükség szerint

**Jó tesztelést!** 🎮
