# Vultaic-MGM-master – <PERSON><PERSON>zletes Rendszerleírás

## Tartalomjegyzék
1. [<PERSON><PERSON><PERSON><PERSON><PERSON>](#áttekin<PERSON>s)
2. [Könyvtárstruktúra és fő modulok](#könyvtárstruktúra-és-fő-modulok)
3. [Core rendszer működése](#core-rendszer-működése)
4. [Adminisz<PERSON><PERSON><PERSON><PERSON> és jogosultságkezelési modulok](#adminisztrációs-és-jogosultságkezelési-modulok)
5. [Szinkronizációs és utilitás modulok](#szinkronizációs-és-utilitás-modulok)
6. [Adatbázis-kezelés és szerver követelmények](#adatbázis-kezelés-és-szerver-követelmények)
7. [Fórum alapú regisztráció és bejelentkezés](#fórum-alapú-regisztráció-és-bejelentkezés)
8. [Biztonsági architektúra](#biztonsági-architektúra)
9. [Modularitás, bővíthetőség és funkciók](#modularitás-bővíthetőség-és-funkciók)
10. [Telepítési és üzemeltetési útmutató (Linux VPS)](#telepítési-és-üzemeltetési-útmutató-linux-vps)

---

## Áttekintés
A **Vultaic-MGM-master** egy modern, moduláris, többjátékos MTA:SA (Multi Theft Auto: San Andreas) multigamemode rendszer, amely fejlett UI-t, szerver-oldali biztonsági megoldásokat, API integrációkat és központi fórum alapú user managementet kínál. A rendszer célja a könnyű bővíthetőség, magas szintű biztonság, központi adminisztráció és fejlett játékmenet támogatása.

---

## Könyvtárstruktúra és fő modulok

- **[admin]:** Admin panelek, jogosultságkezelés, naplózás, fórum integráció (IPB, runcode, v_admin, stb.)
- **[sync]:** Szinkronizációs és utilitás modulok (v_utils)
- **[vultaic]:** A teljes játékmód magja, minden fő funkció külön resource-ban:
    - **core:** Arénakezelés, dimenziók, játékos mozgatás, spectate, ID kiosztás
    - **v_mysql:** Adatbázis kapcsolat és account/stat kezelés
    - **v_login:** Fórum alapú login, API kommunikáció
    - **v_security:** Serial, név, duplikált account védelem
    - **v_panel, v_lobby, v_achievements, v_clans, v_notify, v_settings, v_toptimes, v_scoreboard, stb.:** Minden főbb játékmód és kiegészítő külön resource-ban

---

## Core rendszer működése
- **A core modul a rendszer szíve:**
    - Arénák regisztrációja, törlése, mozgatás
    - Játékosok mozgatása arénák között, lobbyba helyezés
    - Dimenziók kezelése (izolált játéktér minden arénának)
    - Játékosok egyedi ID-jának kiosztása
    - Spectate és map kompatibilitás ellenőrzés
    - Eventek, parancsok, admin funkciók (pl. `/refreshmaps`)
    - Adatstruktúrák: `core.arenas`, `core.resources`, `core.dimensions`, `core.ids`
    - Minden főbb játékmód (race, dm, stb.) ehhez a core-hoz regisztrálja magát

---

## Adminisztrációs és jogosultságkezelési modulok
- **[admin] könyvtár:**
    - **acpanel, admin, admin2:** Admin panelek, admin parancsok, jogkezelés, naplózás
    - **ipb:** Fórum (Invision Power Board) integráció, API kommunikáció
    - **v_admin:** Speciális Vultaic admin funkciók
    - **Funkciók:** szerver menedzsment, jogkiosztás, naplózás, figyelmeztetések, fórum-alapú hitelesítés támogatása

---

## Szinkronizációs és utilitás modulok
- **[sync] könyvtár:**
    - **v_utils:** Általános segédfüggvények, szerver és kliens oldalon is elérhetőek
    - **Funkció:** szerver-kliensek közötti szinkronizáció, támogató utilitás

---

## Adatbázis-kezelés és szerver követelmények
- **MySQL/MariaDB** szükséges
- **Fő tábla:** `v_accounts` (éles), `v_accounts_dev` (fejlesztői mód)
- **További táblák:** statisztikák, achievementek, tuning, stb. (resource-onként bővülhet)
- **Account tábla tipikus mezői:** id, username, password_hash, email, serial, regisztráció dátuma, playtime, stb.
- **Kapcsolódó resource-ok:** `v_login` (bejelentkezés), `v_awards` (achievement rendszer)
- **Szerver configban (pl. `mysql_server.lua`) kapcsolati adatok:**
    - host: `127.0.0.1`
    - port: `3306`
    - user: `mtauser`
    - password: `erős_jelszó`
    - dbname: `v_accounts`

---

## Fórum alapú regisztráció és bejelentkezés
- **Regisztráció:**
    - A játékos a Vultaic fórumon (Invision Power Board) regisztrál
- **Bejelentkezés:**
    - Szerveren a `v_login` modulon keresztül történik
    - A játékos megadja a fórum felhasználónevét és jelszavát
    - A szerver API hívást indít a fórum felé (`mta_login_test.php` vagy `mta_login_dev.php` endpoint)
    - A fórum oldali PHP script ellenőrzi a hitelesítést (username, password hash)
    - Sikeres hitelesítés esetén a szerver oldalon beállítódnak a szükséges adatok (account_id, username, stb.), és a játékos beléphet
    - **Duplikált login, session, aktív próbálkozások szerver oldalon szigorúan kezelve vannak**
- **Előnyök:**
    - Központi user management, biztonságos hitelesítés, könnyen bővíthető, auditálható folyamat

---

## Biztonsági architektúra
- **Szerver-oldali validáció minden kritikus ponton**
- **Serial, név, duplikált account védelem**
- **Parancs naplózás, chat flood/spam védelem**
- **Jelszavak hash-elve, API-n keresztül történő hitelesítés**
- **Saját biztonsági resource-ok (`v_security`, `v_notify` stb.)**
- **Session kezelés, aktív próbálkozások korlátozása**

---

## Modularitás, bővíthetőség és funkciók
- **Magas modularitás:** minden funkció külön resource-ban
- **Könnyű bővíthetőség:** új játékmódok, funkciók, UI elemek egyszerűen hozzáadhatók
- **Főbb funkciók:**
    - Többféle játékmód (race, dm, shooter, training, tuning, stb.)
    - Achievement rendszer (`v_achievements`)
    - Klán rendszer (`v_clans`)
    - Fejlett lobby, panel, statisztika, tuning, overlay, scoreboard, settings
    - Modern dxlib UI, responsive design, optimalizált LUA szintaxis

---

## Telepítési és üzemeltetési útmutató (Linux VPS)

### 1. MySQL szerver telepítése
```bash
sudo apt install mysql-server
```

### 2. Adatbázis létrehozása
```sql
CREATE DATABASE v_accounts CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 3. Felhasználó létrehozása és jogosultság adása
```sql
CREATE USER 'mtauser'@'localhost' IDENTIFIED BY 'erős_jelszó';
GRANT ALL PRIVILEGES ON v_accounts.* TO 'mtauser'@'localhost';
FLUSH PRIVILEGES;
```

### 4. Szükséges táblák létrehozása
A resource-okhoz tartozó `.sql` vagy dokumentáció alapján (pl. id, username, password_hash, email, stb.).

### 5. Szerver configban a kapcsolati adatok beállítása
- host: `127.0.0.1`
- port: `3306`
- user: `mtauser`
- password: `erős_jelszó`
- dbname: `v_accounts`

### 6. MTA szerver indítása, resource-ok aktiválása
- A `v_mysql`, `v_login`, `v_awards` resource-oknak futniuk kell
- Ellenőrizd a logokat, hogy a MySQL kapcsolat sikeres-e

---

## Záró gondolatok
A Vultaic-MGM-master rendszer egy modern, biztonságos, központilag menedzselhető és könnyen bővíthető multigamemode platform, amely hosszú távon stabil alapot nyújt egy nagyobb közösségi szerverhez is. A részletes modularitás, a szerver-oldali validációk, a fórum alapú hitelesítés és az átlátható adminisztráció mind hozzájárulnak a professzionális működéshez.

_Készült: 2025.08.12._
