# Vultaic MGM Rendszer Áttekintés

## Általános Információk

**Projekt neve:** Vultaic Multigamemode for MTA:SA  
**Szerzők:** DizzasTeR & MIRAGE  
**Licenc:** MIT License (2020)  
**Platform:** Multi Theft Auto: San Andreas (MTA:SA)

## Rendszer Architektúra

### Core Komponensek

#### 1. **Core Rendszer** (`[vultaic]/core/`)

-   **core_server.lua**: Fő szerver logika, arena kezelés
-   **core_client.lua**: Kliens oldali core funkciók
-   **lobby_server.lua**: Lobby kezelés
-   **common.lua**: Közös függ<PERSON>

**Főbb funkciók:**

-   Arena regisztráció és kezelés
-   Játékos dimenzió kezelés
-   Lobby rendszer
-   Játékos ID hozzárendelés

#### 2. **Biztonsági Ren<PERSON>** (`[vultaic]/v_security/`)

-   **security_server.lua**: Biztonsági ellen<PERSON>rz<PERSON>

**Biztonsági intézkedések:**

-   Serial szám ellenőrz<PERSON> kap<PERSON>olódáskor
-   Nickname validáció (min. 3 karakter)
-   Duplikált nevek megakadályozása
-   Parancs naplózás
-   Debug script hozzáférés korlátozás
-   Mute állapotban nickname változtatás tiltása

#### 3. **Adatbázis Rendszer** (`[vultaic]/v_mysql/`)

-   **mysql_server.lua**: MySQL kapcsolat kezelés
-   **CAccount.lua**: Felhasználói fiók osztály
-   **mysql_client.lua**: Kliens oldali adatbázis műveletek

**Adatbázis konfiguráció:**

-   Host: 127.0.0.1:3306
-   Adatbázis: `v_accounts` (production) / `v_accounts_dev` (development)
-   Felhasználó: root
-   Jelszó: M1RAg3_Zz@ST3R

**Tárolt adatok:**

-   Felhasználói statisztikák (pénz, pontok)
-   Játékmód specifikus pontok (DM, Race, Shooter, Hunter, stb.)
-   Klán tagság
-   Tuning beállítások
-   Díjak és eredmények

#### 4. **Bejelentkezési Rendszer** (`[vultaic]/v_login/`)

-   **login_server.lua**: Bejelentkezés kezelés
-   **avatars_server.lua**: Avatar kezelés

**API kapcsolat:**

-   Production: https://forum.vultaic.com/mta_login_test.php
-   Development: https://forum.vultaic.com/mta_login_dev.php

### Játékmódok és Modulok

#### Főbb Játékmódok:

1. **Race** (`[vultaic]/v_race/`)
2. **Deathmatch** (`[vultaic]/v_dm/`)
3. **Hunter** (`[vultaic]/v_hunter/`)
4. **Shooter** (`[vultaic]/v_shooter/`)
5. **Training** (`[vultaic]/v_training_*`)

#### Kiegészítő Modulok:

-   **Garage** (`[vultaic]/v_garage/`) - Jármű testreszabás
-   **Chat** (`[vultaic]/v_chat/`) - Chat rendszer
-   **Clans** (`[vultaic]/v_clans/`) - Klán rendszer
-   **Achievements** (`[vultaic]/v_achievements/`) - Eredmények
-   **Music** (`[vultaic]/v_music/`) - Zene lejátszás
-   **Radar** (`[vultaic]/v_radar/`) - Minimap
-   **Scoreboard** (`[vultaic]/v_scoreboard/`) - Eredménytábla

### Admin Rendszer

#### Admin Modulok (`[admin]/`):

1. **v_admin** - Alapvető admin parancsok
2. **acpanel** - Admin control panel
3. **admin** - Részletes admin rendszer
4. **runcode** - Kód futtatás (fejlesztői)

#### Admin Szintek:

-   **Szint 1**: Alapfelhasználó
-   **Szint 2**: Moderátor (kick, mute, warn)
-   **Szint 3**: Super Moderátor (ban, weather, time)
-   **Szint 4**: Fejlesztő (map kezelés, események)
-   **Szint 5**: Tulajdonos (tag kezelés, teljes hozzáférés)

## Biztonsági Megfelelőség

### ✅ Pozitív Biztonsági Elemek:

1. **Hozzáférés Kontroll:**

    - Szintezett admin rendszer
    - Parancs jogosultság ellenőrzés
    - Serial alapú azonosítás

2. **Input Validáció:**

    - Nickname hossz ellenőrzés
    - Paraméter típus ellenőrzés
    - SQL injection védelem (prepared statements)

3. **Naplózás:**

    - Parancs használat naplózása
    - Admin műveletek naplózása
    - Biztonsági események naplózása

4. **Session Kezelés:**
    - Egyedi session azonosítók
    - Duplikált bejelentkezés megakadályozása
    - Automatikus kijelentkezés

### ⚠️ Potenciális Biztonsági Kockázatok:

1. **Adatbázis Hozzáférés:**

    - Hardkódolt adatbázis jelszó a kódban
    - Root felhasználó használata
    - Lokális adatbázis (127.0.0.1)

2. **Külső API Hívások:**

    - HTTP kapcsolat a forum API-val
    - Nincs SSL tanúsítvány validáció

3. **Fájl Kezelés:**
    - Dinamikus fájl létrehozás és törlés
    - Temp fájlok kezelése

## Kódolt Fájlok Vizsgálata

### 🔍 Talált .luac Fájlok:

A `[vultaic]/v_garage/meta.xml` fájlban található .luac hivatkozások:

-   `util_client.luac`
-   `dxlib_client.luac`
-   `garage_client.luac`
-   `camera_client.luac`
-   `tabs/colors.luac`
-   `tabs/tints.luac`
-   `tabs/wheels.luac`
-   `tabs/bodyparts.luac`
-   `tabs/stickers.luac`
-   `tabs/stickers_catalog.luac`
-   `tabs/lights.luac`
-   `tabs/overlays.luac`
-   `tabs/neons.luac`
-   `tabs/rocketcolor.luac`
-   `tabs/skins.luac`

### ⚠️ Biztonsági Megjegyzések a .luac Fájlokról:

1. **Kódolt Tartalom:** Ezek a fájlok Lua bytecode formátumban vannak, ami megnehezíti a kód áttekintését
2. **Garage Modul:** A teljes garage rendszer kliens oldala kódolt
3. **Átláthatóság Hiánya:** Nem lehet ellenőrizni, hogy ezek a fájlok tartalmaznak-e:
    - Hátsó ajtókat (backdoors)
    - Adatszivárogtatást
    - Rosszindulatú kódot

### 🔧 Kód Fordítási Rendszer:

A `security_server.lua`-ban található `compileres` parancs:

-   Külső szolgáltatást használ: `http://luac.mtasa.com/`
-   Automatikus kód obfuszkáció (obfuscate=2)
-   Debug információk eltávolítása

## Adatáramlás és Kapcsolatok

### Adatbázis Kapcsolatok:

```
MTA Server ←→ MySQL (localhost:3306)
    ↓
v_accounts / v_accounts_dev
    ↓
Felhasználói adatok, statisztikák, beállítások
```

### Külső Kapcsolatok:

```
MTA Server ←→ forum.vultaic.com (HTTPS)
    ↓
Felhasználói hitelesítés

MTA Server ←→ luac.mtasa.com (HTTP)
    ↓
Kód fordítás és obfuszkáció
```

### Belső Modulok Kommunikációja:

```
Core ←→ MySQL ←→ Login ←→ Security
  ↓      ↓       ↓       ↓
Arena  Stats   Auth   Validation
```

## Teljesítmény és Skálázhatóság

### Erősségek:

-   Moduláris felépítés
-   Dimenzió alapú elkülönítés
-   Hatékony arena kezelés
-   Gyorsítótárazott adatok

### Gyengeségek:

-   Egyetlen adatbázis szerver
-   Szinkron adatbázis műveletek
-   Memóriában tárolt session adatok

## Összegzés és Ajánlások

### ✅ Pozitívumok:

-   Jól strukturált, moduláris kód
-   Átfogó admin rendszer
-   Részletes naplózás
-   Többszintű hozzáférés kontroll

### ⚠️ Kritikus Problémák:

1. **Hardkódolt jelszavak** - Azonnali javítás szükséges
2. **Kódolt .luac fájlok** - Biztonsági kockázat, átláthatóság hiánya
3. **Root adatbázis hozzáférés** - Korlátozott jogosultságú felhasználó használata javasolt

### 🔒 Biztonsági Ajánlások:

1. Környezeti változók használata érzékeny adatokhoz
2. .luac fájlok dekódolása és áttekintése
3. Adatbázis felhasználó jogosultságainak korlátozása
4. SSL/TLS használata külső API hívásokhoz
5. Input sanitization fejlesztése
6. Rendszeres biztonsági audit

### 📊 Általános Értékelés:

A rendszer alapvetően jól megtervezett és funkcionális, azonban tartalmaz kritikus biztonsági hiányosságokat, különösen a kódolt fájlok és hardkódolt hitelesítési adatok terén. A .luac fájlok jelenléte miatt nem lehet teljes mértékben garantálni, hogy a rendszer mentes a hátsó ajtóktól vagy adatszivárogtatástól.

## Részletes Modul Analízis

### Script Loader és Map Manager

#### Script Loader (`[vultaic]/scriptloader/`)

-   **Sandbox környezet**: Biztonságos script futtatás
-   **Dinamikus script betöltés**: Külső map fájlokból
-   **Fájl hozzáférés korlátozás**: Sandbox-on belüli fájlműveletek
-   **Tiltott billentyűk**: Bizonyos billentyű kombinációk letiltása

#### Map Manager (`[vultaic]/mapmanager/`)

-   **Titkosított map cache**: TEA algoritmus használata
-   **Fájl integritás ellenőrzés**: MD5 hash alapú
-   **Dinamikus letöltés**: Hiányzó fájlok automatikus letöltése
-   **Verzió kezelés**: Map fájlok verziózása

### Testreszabási Rendszerek

#### Garage Rendszer (`[vultaic]/v_garage/`)

**⚠️ KRITIKUS BIZTONSÁGI FIGYELMEZTETÉS:**

-   Teljes kliens oldali kód .luac formátumban
-   Nem ellenőrizhető tartalom
-   Potenciális backdoor lehetőség

#### Body Parts (`[vultaic]/v_body/`)

-   **Titkosított DFF fájlok**: Base64 + TEA titkosítás
-   **Dinamikus model betöltés**: Runtime model csere
-   **Temp fájl kezelés**: Ideiglenes fájlok létrehozása/törlése

### Kommunikációs Rendszerek

#### Chat Rendszer (`[vultaic]/v_chat/`)

-   **Többcsatornás chat**: Különböző chat típusok
-   **Spam védelem**: Üzenet gyakoriság korlátozás
-   **Admin chat**: Elkülönített admin kommunikáció

#### Notification Rendszer

-   **Központi értesítések**: Egységes notification kezelés
-   **Típus alapú megjelenítés**: Különböző notification típusok
-   **Automatikus eltűnés**: Időzített notification eltávolítás

### Játékos Statisztika Rendszer

#### Statisztika Típusok:

-   **Alapvető**: Pénz, szint, játékidő
-   **Játékmód specifikus**: DM, Race, Shooter pontok
-   **Tuning**: Jármű testreszabási beállítások
-   **Eredmények**: Díjak, rekordok, ranglisták

#### Adatmentés:

-   **Valós idejű**: Folyamatos statisztika frissítés
-   **Batch mentés**: Kilépéskor teljes adatmentés
-   **JSON szerializáció**: Komplex adatok tárolása

### Sync és Utility Rendszerek

#### V_Utils (`[sync]/v_utils/`)

-   **Szinkronizációs segédeszközök**
-   **Közös utility függvények**
-   **Cross-resource kommunikáció**

### Admin és Moderációs Eszközök

#### Részletes Admin Parancsok:

-   **Játékos kezelés**: kick, ban, mute, warn
-   **Szerver kezelés**: weather, time, gamespeed
-   **Map kezelés**: map váltás, tag kezelés, törlés
-   **Statisztika kezelés**: HP, pénz, pontok módosítása

#### Jogosultság Rendszer:

```
Szint 1 (User) → Szint 2 (Mod) → Szint 3 (Super Mod) → Szint 4 (Dev) → Szint 5 (Owner)
```

### Fájl Rendszer Biztonsági Elemzés

#### Titkosítási Módszerek:

1. **TEA (Tiny Encryption Algorithm)**: Map cache és DFF fájlok
2. **Base64**: Bináris adatok kódolása
3. **MD5**: Fájl integritás ellenőrzés
4. **Luac**: Lua bytecode fordítás

#### Fájl Típusok és Kockázatok:

-   **.lua**: Nyílt forráskód ✅
-   **.luac**: Kódolt bytecode ⚠️
-   **.edf**: Titkosított map data ⚠️
-   **.edk**: Titkosítási kulcsok ⚠️

### Hálózati Kommunikáció

#### Belső Events:

-   **Szerver ↔ Kliens**: Kétirányú event kommunikáció
-   **Resource közötti**: Cross-resource function hívások
-   **Trigger rendszer**: Event alapú architektúra

#### Külső Kapcsolatok:

-   **Forum API**: HTTPS bejelentkezés
-   **Luac Service**: HTTP kód fordítás
-   **MySQL**: Lokális adatbázis kapcsolat

### Teljesítmény Optimalizáció

#### Gyorsítótárazás:

-   **Map cache**: Titkosított map adatok
-   **Avatar cache**: Felhasználói avatárok
-   **Statisztika cache**: Memóriában tárolt player adatok

#### Lazy Loading:

-   **Resource betöltés**: Igény szerinti modul aktiválás
-   **Map letöltés**: Csak szükséges fájlok letöltése
-   **Texture streaming**: Dinamikus texture betöltés

## Kritikus Biztonsági Megállapítások

### 🚨 Azonnali Beavatkozást Igénylő Problémák:

1. **Hardkódolt Adatbázis Jelszó (4 fájlban)**

    **Érintett fájlok:**
    - `v_mysql/mysql_server.lua:17`
    - `v_toptimes/toptimes_server.lua:11` 
    - `v_clans/clan_server.lua:14`
    - `v_achievements/achievements_s.lua:30`

    ```lua
    g_Connection, error = dbConnect("mysql", "dbname="..DB_NAME..";host=127.0.0.1;port=3306", "root", "M1RAg3_Zz@ST3R")
    ```

    - **Kockázat**: Teljes adatbázis hozzáférés kompromittálása
    - **Megoldás**: Környezeti változók vagy config fájl használata

2. **Kódolt .luac Fájlok (29 darab összesen)**

    **Érintett modulok:**
    - **v_garage**: 15 .luac fájl
    - **v_panel**: 14 .luac fájl
    
    - **Kockázat**: Nem ellenőrizhető kód, potenciális backdoor
    - **Megoldás**: Dekompilálás és forráskód audit

3. **Külső API Kulcsok Hardkódolva**
    ```lua
    fetchRemote("http://api.ipinfodb.com/v3/ip-country/?key=****************************************************************&format=json&ip="..getPlayerIP(player)
    ```
    - **Fájl**: `v_scoreboard/scoreboard_server.lua:19`
    - **Kockázat**: API kulcs visszaélés, költségek növekedése
    - **Megoldás**: API kulcs externalizálása

4. **Külső Kód Fordítási Szolgáltatás**
    ```lua
    fetchRemote("http://luac.mtasa.com/?compile=1&debug=0&obfuscate=2", ...)
    ```
    - **Kockázat**: Külső szolgáltatás kompromittálása esetén kártékony kód injektálás
    - **Megoldás**: Lokális fordítás vagy megbízható szolgáltatás használata

### 🔍 Részletes Backdoor Keresési Eredmények:

**Vizsgált területek:**

-   ✅ Core server logika - Tiszta
-   ✅ MySQL kapcsolat kezelés - Tiszta (jelszó kivételével)
-   ✅ Login rendszer - Tiszta
-   ✅ Admin parancsok - Tiszta
-   ⚠️ Garage .luac fájlok - Nem ellenőrizhető
-   ✅ Security modul - Tiszta
-   ✅ Map manager - Tiszta (titkosítás normális)

**Gyanús elemek:**

-   Garage modul teljes kliens oldali kódja kódolt
-   Külső luac.mtasa.com szolgáltatás használata
-   TEA titkosítás kulcsok fájlrendszerben tárolva

### 📋 Megfelelőségi Jelentés:

**GDPR/Adatvédelmi Megfelelőség:**

-   ❌ Jelszavak nyílt szövegben tárolva a kódban
-   ✅ Felhasználói adatok titkosítva az adatbázisban
-   ✅ Session kezelés megfelelő
-   ⚠️ Naplózás személyes adatokat tartalmazhat

**Kód Minőség:**

-   ✅ Moduláris architektúra
-   ✅ Error handling implementált
-   ✅ Dokumentáció részben elérhető
-   ⚠️ Kódolt részek miatt teljes audit nem lehetséges

**Biztonsági Megfelelőség:**

-   ⚠️ Részben megfelelő
-   🚨 Kritikus sebezhetőségek jelen vannak
-   🔍 Teljes audit szükséges a .luac fájlok dekódolása után

### 🎯 Prioritásos Javítási Terv:

**1. Prioritás (Azonnali):**

-   Adatbázis jelszó externalizálása
-   .luac fájlok dekompilálása és audit
-   Root adatbázis hozzáférés korlátozása

**2. Prioritás (1 héten belül):**

-   SSL/TLS implementálása külső API hívásokhoz
-   Input validáció fejlesztése
-   Naplózás személyes adatok szűrése

**3. Prioritás (1 hónapon belül):**

-   Teljes kód audit befejezése
-   Penetrációs teszt végrehajtása
-   Biztonsági dokumentáció frissítése

## Végső Értékelés

**Biztonsági Besorolás: MAGAS KOCKÁZAT** 🔴

A rendszer alapvetően biztonságos architektúrával rendelkezik, azonban a **4 fájlban hardkódolt adatbázis jelszavak**, **29 kódolt .luac fájl**, és **hardkódolt API kulcsok** kritikus biztonsági kockázatot jelentenek. Azonnali beavatkozás szükséges.

**Részletes elemzés:** `BIZTONSAGI_ELEMZES_RESZLETES_HU.md`  
**Teljes kódbázis index:** `KODABAZIS_INDEX_HU.md`
