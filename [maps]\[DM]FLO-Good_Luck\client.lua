function startclient()
setWaterColor(105,125,255)
end
addEventHandler( "onClientResourceStart", getResourceRootElement(getThisResource()), startclient )



outputChatBox ( " " , 0 , 255 , 255)
outputChatBox ( " " , 0 , 255 , 255)
outputChatBox ( " " , 0 , 255 , 255)
outputChatBox ( " " , 0 , 255 , 255)
outputChatBox ( " " , 0 , 255 , 255)
outputChatBox ( " " , 0 , 255 , 255)
outputChatBox ( " " , 0 , 255 , 255)
outputChatBox ( " " , 0 , 255 , 255)
outputChatBox ( " " , 0 , 255 , 255)
outputChatBox ( " " , 0 , 255 , 255)
outputChatBox ( " " , 0 , 255 , 255)
outputChatBox ( " " , 0 , 255 , 255)
outputChatBox ( " " , 0 , 255 , 255)
outputChatBox ( " " , 0 , 255 , 255)
outputChatBox ( " " , 0 , 255 , 255)
outputChatBox ( " " , 0 , 255 , 255)
outputChatBox ( "FLO : Hello !" , 105 , 121 , 255)
outputChatBox ( " " , 0 , 255 , 255)
outputChatBox ( "[DM]FLO - Good Luck " , 255 ,0 , 45)
outputChatBox ( " " , 0 , 255 , 255)
outputChatBox ( "Press 'M' to music On/off" , 0 , 255 , 125)
outputChatBox ( " " , 0 , 255 , 255)