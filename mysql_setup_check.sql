-- MySQL Setup <PERSON>t
-- Futtasd: mysql -u root -p < mysql_setup_check.sql

-- 1. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ellenőrz<PERSON>e
SELECT User, Host FROM mysql.user WHERE User IN ('sunuser', 'root');

-- 2. Adatbázisok ellenőrzése
SHOW DATABASES LIKE 'v_%';
SHOW DATABASES LIKE 'mta_%';

-- 3. sunuser jogosultságainak ellenőrzése
SHOW GRANTS FOR 'sunuser'@'localhost';

-- 4. <PERSON>tb<PERSON><PERSON><PERSON> kapcsolat teszt
USE v_accounts;
SHOW TABLES;

USE v_accounts_dev;
SHOW TABLES;

USE mta_toptimes;
SHOW TABLES;

-- 5. vmtasa_accounts tábla struktúra ellenőrzése
USE v_accounts;
DESCRIBE vmtasa_accounts;

-- Ha a fenti parancsok hibát adnak, akkor futtasd az alábbi setup parancsokat:

/*
-- SETUP PARANCSOK (csak ha szükséges):

-- Adatbázisok létrehozása
CREATE DATABASE IF NOT EXISTS v_accounts CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS v_accounts_dev CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS mta_toptimes CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- sunuser létrehozása és jogosultságok
CREATE USER IF NOT EXISTS 'sunuser'@'localhost' IDENTIFIED BY 'SunUser2025@&#';
GRANT ALL PRIVILEGES ON v_accounts.* TO 'sunuser'@'localhost';
GRANT ALL PRIVILEGES ON v_accounts_dev.* TO 'sunuser'@'localhost';
GRANT ALL PRIVILEGES ON mta_toptimes.* TO 'sunuser'@'localhost';
FLUSH PRIVILEGES;

-- Fő tábla létrehozása
USE v_accounts;
CREATE TABLE IF NOT EXISTS vmtasa_accounts (
    account_id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(32) NOT NULL UNIQUE,
    password_hash VARCHAR(128) NOT NULL,
    email VARCHAR(100),
    serial VARCHAR(32),
    
    -- Pénz és pontok
    money INT DEFAULT 0,
    dm_points INT DEFAULT 0,
    os_points INT DEFAULT 0,
    dd_points INT DEFAULT 0,
    race_points INT DEFAULT 0,
    shooter_points INT DEFAULT 0,
    hunter_points INT DEFAULT 0,
    tdm_points INT DEFAULT 0,
    
    -- Klán
    Clan VARCHAR(32) DEFAULT NULL,
    
    -- JSON adatok
    data JSON DEFAULT NULL,
    tuning JSON DEFAULT NULL,
    awards JSON DEFAULT NULL,
    
    -- Időbélyegek
    regdate TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Indexek
    INDEX idx_username (username),
    INDEX idx_serial (serial),
    INDEX idx_clan (Clan)
);

-- Ugyanez a fejlesztői adatbázisban
USE v_accounts_dev;
CREATE TABLE IF NOT EXISTS vmtasa_accounts (
    account_id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(32) NOT NULL UNIQUE,
    password_hash VARCHAR(128) NOT NULL,
    email VARCHAR(100),
    serial VARCHAR(32),
    money INT DEFAULT 0,
    dm_points INT DEFAULT 0,
    os_points INT DEFAULT 0,
    dd_points INT DEFAULT 0,
    race_points INT DEFAULT 0,
    shooter_points INT DEFAULT 0,
    hunter_points INT DEFAULT 0,
    tdm_points INT DEFAULT 0,
    Clan VARCHAR(32) DEFAULT NULL,
    data JSON DEFAULT NULL,
    tuning JSON DEFAULT NULL,
    awards JSON DEFAULT NULL,
    regdate TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_serial (serial),
    INDEX idx_clan (Clan)
);

-- Achievement tábla
USE v_accounts;
CREATE TABLE IF NOT EXISTS v_achievements (
    id INT PRIMARY KEY AUTO_INCREMENT,
    account_id INT NOT NULL,
    achievement_id INT NOT NULL,
    unlocked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (account_id) REFERENCES vmtasa_accounts(account_id) ON DELETE CASCADE,
    UNIQUE KEY unique_achievement (account_id, achievement_id),
    INDEX idx_account (account_id),
    INDEX idx_achievement (achievement_id)
);

USE v_accounts_dev;
CREATE TABLE IF NOT EXISTS v_achievements (
    id INT PRIMARY KEY AUTO_INCREMENT,
    account_id INT NOT NULL,
    achievement_id INT NOT NULL,
    unlocked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (account_id) REFERENCES vmtasa_accounts(account_id) ON DELETE CASCADE,
    UNIQUE KEY unique_achievement (account_id, achievement_id),
    INDEX idx_account (account_id),
    INDEX idx_achievement (achievement_id)
);

-- Klán táblák létrehozása
USE v_accounts;
CREATE TABLE IF NOT EXISTS v_clans (
    clan_id INT PRIMARY KEY AUTO_INCREMENT,
    clan_name VARCHAR(32) NOT NULL UNIQUE,
    clan_tag VARCHAR(8) NOT NULL UNIQUE,
    leader_id INT NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (leader_id) REFERENCES vmtasa_accounts(account_id),
    INDEX idx_name (clan_name),
    INDEX idx_tag (clan_tag)
);

CREATE TABLE IF NOT EXISTS v_clan_members (
    id INT PRIMARY KEY AUTO_INCREMENT,
    clan_id INT NOT NULL,
    account_id INT NOT NULL,
    rank ENUM('member', 'moderator', 'admin', 'leader') DEFAULT 'member',
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (clan_id) REFERENCES v_clans(clan_id) ON DELETE CASCADE,
    FOREIGN KEY (account_id) REFERENCES vmtasa_accounts(account_id) ON DELETE CASCADE,
    UNIQUE KEY unique_membership (clan_id, account_id)
);

-- Fejlesztői adatbázisban is
USE v_accounts_dev;
CREATE TABLE IF NOT EXISTS v_clans (
    clan_id INT PRIMARY KEY AUTO_INCREMENT,
    clan_name VARCHAR(32) NOT NULL UNIQUE,
    clan_tag VARCHAR(8) NOT NULL UNIQUE,
    leader_id INT NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (leader_id) REFERENCES vmtasa_accounts(account_id),
    INDEX idx_name (clan_name),
    INDEX idx_tag (clan_tag)
);

CREATE TABLE IF NOT EXISTS v_clan_members (
    id INT PRIMARY KEY AUTO_INCREMENT,
    clan_id INT NOT NULL,
    account_id INT NOT NULL,
    rank ENUM('member', 'moderator', 'admin', 'leader') DEFAULT 'member',
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (clan_id) REFERENCES v_clans(clan_id) ON DELETE CASCADE,
    FOREIGN KEY (account_id) REFERENCES vmtasa_accounts(account_id) ON DELETE CASCADE,
    UNIQUE KEY unique_membership (clan_id, account_id)
);
*/
