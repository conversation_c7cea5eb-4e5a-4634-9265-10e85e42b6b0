--[[
	VULTAIC MGM - INFORMÁCIÓS RENDSZER SZERVER OLDAL

	Ez a fájl az automatikus információs üzenetek rendszerét tartalmazza.

	Főbb funkciók:
	- Automatikus info üzenetek küldése
	- Ciklikus üzenet rotáció
	- Színkód formázás
	- Timer alapú ütemezés

	Működés:
	- 10 percenként küld egy info üzenetet
	- Az üzenetek ciklikusan váltakoznak
	- Speciális s<PERSON>kódolás a fontos részekhez
	- Minden játékos megkapja az üzeneteket
]]

-- Információs üzenetek listája
local messages = {
	"Visit us at [www.vultaic.com]",  -- Weboldal
	"Join our discord [https://discordapp.com/invite/g7bp2Bq] to be notified of our development and exciting [sneak peaks]!",  -- Discord
	"If you'd like to ignore messages from a certain player, you can do so using [/ignore]",  -- Ignore parancs
    "Become a donator to enjoy our exclusive garage upgrades.",  -- Donator el<PERSON><PERSON>
    "Are you having FPS issues? Take a look at the settings. ([F7 > Settings])",  -- FPS beállítások
    "Create your clan today for $500K, 50% off for donators! ([F7 > Clans])",  -- Clan létrehozás
    "Bugged map(-s)? Report them at [www.vultaic.com]!",  -- Bug jelentés
    "Help us improve your gameplay experience by posting your ideas at our suggestions sub-forum.",  -- Javaslatok
    "All available controls, commands and server rules can be found at our help panel. ([F7 > Help])",  -- Súgó panel
}

-- Jelenlegi üzenet index (1-től kezdve)
local i = 1  -- math.random(#messages) helyett szekvenciális

--[[
	Random üzenet kiírása

	Ez a függvény kiír egy információs üzenetet a chatbe.
	Az üzenetek ciklikusan váltakoznak, és speciális formázást kapnak.
]]
function outputRandomMessage()
	-- Következő üzenetre lépés
	i = i + 1
	if i > #messages then
		i = 1  -- Visszatérés az első üzenetre
	end

	-- Üzenet előkészítése
	message = messages[i]

	-- Színkódok eltávolítása (ha vannak)
	message = string.gsub(message, "#%x%x%x%x%x%x", "")

	-- Speciális formázás: [ ] jelek színezése
	message = string.gsub(message, "%[", "#19846D")  -- Nyitó zárójel zöld színnel
	message = string.gsub(message, "%]", "#FFFFFF")  -- Záró zárójel fehér színnel

	-- Üzenet kiírása minden játékosnak
	outputChatBox("[INFO] #FFFFFF"..message, root, 25, 132, 109, true)
end

-- Első üzenet azonnali kiírása
outputRandomMessage()

-- Timer beállítása: 10 percenként (60000 ms * 10 = 600000 ms)
setTimer(outputRandomMessage, 60000 * 10, 0)