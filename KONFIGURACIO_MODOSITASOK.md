# Vultaic MGM - Kon<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ítások

## 📋 Áttekintés

Ez a dokumentum összefoglalja a **mtaserver.conf** és **acl.xml** fájlok szükséges módosításait a Vultaic MGM rendszer optimális működéséhez.

---

## 🔧 mtaserver.conf Módosítások

### ✅ **OPTIMALIZÁLT VERZIÓ** (Ajánlott)

A jelenlegi konfiguráció **redundáns**, mert a **core resource automatikusan elindítja** az összes szükséges Vultaic resource-ot.

#### Előnyök:

-   **Tisztább konfiguráció** - Kevesebb sor, könnyebb karbantartás
-   **Automatikus függőség kezelés** - A core gondoskodik a resource-okról
-   **Hibamentes indítás** - Hely<PERSON> sorrend garantált

#### Módosított resource lista:

```xml
<!-- OPTIMALIZÁLT VERZIÓ: A core automatikusan elindítja a szükséges resource-okat -->
<!-- Csak a core és admin resource-ok szükségesek manuálisan -->

<!-- Fő core resource (ez elindítja az összes Vultaic resource-ot) -->
<resource src="core" startup="1" protected="0" />

<!-- Admin és szinkronizációs modulok (ezek nem szerepelnek a core meta.xml-ben) -->
<resource src="acpanel" startup="1" protected="0" />
<resource src="admin" startup="1" protected="0" />
<resource src="admin2" startup="1" protected="0" />
<resource src="ipb" startup="1" protected="0" />
<resource src="runcode" startup="1" protected="0" />
<resource src="v_admin" startup="1" protected="0" />

<!-- Szinkronizációs modulok -->
<resource src="v_utils" startup="1" protected="0" />
```

#### Automatikusan elindított resource-ok:

A **core** resource automatikusan elindítja ezeket:

-   `mapmanager`, `scriptloader`
-   `v_mysql`, `v_login`, `v_security`, `v_achievements`, `v_clans`
-   `v_lobby`, `v_panel`, `v_notify`, `v_settings`, `v_scoreboard`
-   `v_dm`, `v_race`, `v_shooter`, `v_hunter`, `v_os`, `v_fdd`, `v_hdm`
-   `v_tuning`, `v_body`, `v_paint`, `v_wheels`, `v_overlays`, `v_lights`, `v_neons`
-   `v_chat`, `v_locale`, `v_donatorship`, `v_avatars`, `v_deathlist`
-   `v_racehud`, `v_radar`, `v_shaders`, `v_toptimes`, `v_garage`
-   És még sok más...

### 🔄 **JELENLEGI VERZIÓ** (Működik, de redundáns)

Ha megtartod a jelenlegi verziót, az is működni fog, csak:

-   Hosszabb a konfiguráció
-   Duplikált resource indítások (nem probléma, de felesleges)
-   Nehezebb karbantartani

---

## 🛡️ ACL.xml Módosítások

### ⚠️ **KRITIKUS VÁLTOZTATÁSOK**

#### 1. **Admin Serial Hozzáadása**

```xml
<group name="Admin">
    <!-- ⚠️ FONTOS: Add hozzá a saját serial-odat! -->
    <!-- Példa: <object name="user.1234567890ABCDEF1234567890ABCDEF" /> -->
    <object name="user.D3D2AB2339790C36BAA9CCB6A96BBFF4" />  <!-- CSERÉLD KI A SAJÁT SERIAL-ODRA! -->
</group>
```

**Hogyan szerezd meg a serial-odat:**

1. **MTA kliensben:** Settings → Advanced → Show serial
2. **Játékban:** `/serial` parancs
3. **32 karakter hosszú** hexadecimális string

#### 2. **Vultaic-specifikus Jogosultságok**

Hozzáadott admin parancsok:

```xml
<!-- Vultaic specifikus parancsok -->
<right name="command.setb" access="true" />
<right name="command.get" access="true" />
<right name="command.gete" access="true" />
<right name="command.vlogin" access="true" />
<right name="command.reloadawards" access="true" />
<right name="command.award" access="true" />
<right name="command.refreshmaps" access="true" />
```

Vultaic resource jogosultságok:

```xml
<!-- Vultaic resource jogosultságok -->
<right name="resource.v_mysql" access="true" />
<right name="resource.v_login" access="true" />
<right name="resource.v_achievements" access="true" />
<right name="resource.v_clans" access="true" />
<right name="resource.v_panel" access="true" />
<right name="resource.v_notify" access="true" />
<right name="resource.v_security" access="true" />
<right name="resource.v_settings" access="true" />
<right name="resource.v_scoreboard" access="true" />
<right name="resource.core" access="true" />
```

#### 3. **Optimalizált Csoport Struktúra**

```xml
<!-- ADMIN CSOPORT - TELJES JOGOSULTSÁG -->
<group name="Admin">
    <acl name="Moderator" />
    <acl name="SuperModerator" />
    <acl name="Admin" />
    <acl name="RPC" />
    <object name="user.YOUR_SERIAL_HERE" />  <!-- ⚠️ MÓDOSÍTSD! -->
</group>

<!-- SUPERMODERATOR CSOPORT -->
<group name="SuperModerator">
    <acl name="Moderator" />
    <acl name="SuperModerator" />
    <!-- <object name="user.SUPERMODERATOR_SERIAL_HERE" /> -->
</group>

<!-- MODERATOR CSOPORT -->
<group name="Moderator">
    <acl name="Moderator" />
    <!-- <object name="user.MODERATOR_SERIAL_HERE" /> -->
</group>
```

---

## 🚀 Telepítési Lépések

### 1. **Serial Lekérése**

```bash
# MTA kliensben indítsd el a játékot
# Menj a Settings → Advanced → Show serial menübe
# Vagy használd a /serial parancsot játékban
```

### 2. **ACL.xml Módosítása**

```bash
# Szerkeszd az ACL fájlt
sudo nano /opt/mta/mods/deathmatch/acl.xml

# Keresendő sor:
<object name="user.test1" />

# Cseréld ki:
<object name="user.YOUR_ACTUAL_SERIAL_HERE" />
```

### 3. **mtaserver.conf Optimalizálása** (Opcionális)

```bash
# Ha optimalizálni szeretnéd:
sudo nano /opt/mta/mods/deathmatch/mtaserver.conf

# Töröld a felesleges resource sorokat
# Hagyd meg csak a core-t és az admin resource-okat
```

### 4. **Konfiguráció Tesztelése**

```bash
# XML szintaxis ellenőrzése
xmllint /opt/mta/mods/deathmatch/acl.xml
xmllint /opt/mta/mods/deathmatch/mtaserver.conf

# Szerver indítása
cd /opt/mta
sudo -u mtaserver ./mta-server64
```

### 5. **ACL Újratöltése** (Szerver újraindítás nélkül)

```bash
# MTA konzolban vagy játékban admin parancsként:
aclreload

# Vagy játékban:
/aclreload
```

---

## ✅ Ellenőrzési Lista

### Szerver Indítás Után:

-   [ ] **Core resource betöltődött** - `list` parancs a konzolban
-   [ ] **Összes Vultaic resource fut** - Ellenőrizd a `[running]` státuszt
-   [ ] **Admin jogosultságok működnek** - Próbáld ki az admin parancsokat
-   [ ] **MySQL kapcsolat működik** - Nézd a script logokat

### Admin Tesztelés:

-   [ ] **Bejelentkezés után admin vagy** - `/aclrequest` parancs
-   [ ] **Admin parancsok elérhetők** - `/kick`, `/ban`, `/setb` stb.
-   [ ] **Admin panel elérhető** - Ha van webadmin
-   [ ] **Vultaic parancsok működnek** - `/vlogin`, `/award` stb.

---

## 🐛 Gyakori Problémák

### 1. **"Access denied" admin parancsoknál**

```bash
# Ellenőrizd a serial-t az ACL-ben
# Újratöltés: /aclreload
```

### 2. **Resource-ok nem indulnak el**

```bash
# Ellenőrizd a core resource státuszát
# Nézd a server.log és scripts.log fájlokat
```

### 3. **XML szintaxis hiba**

```bash
# Ellenőrizd az XML fájlokat:
xmllint /opt/mta/mods/deathmatch/acl.xml
xmllint /opt/mta/mods/deathmatch/mtaserver.conf
```

---

## 📝 Összefoglalás

### ✅ **Ajánlott Konfiguráció:**

1. **mtaserver.conf** - Optimalizált verzió (csak core + admin resource-ok)
2. **acl.xml** - Frissített Vultaic-specifikus jogosultságokkal
3. **Admin serial** - Saját serial hozzáadása az Admin csoporthoz

### 🎯 **Eredmény:**

-   Tisztább, karbantarthatóbb konfiguráció
-   Teljes admin jogosultságok
-   Optimális resource betöltési sorrend
-   Vultaic-specifikus funkciók elérhetősége

---

**Készítette:** AI Assistant  
**Utolsó frissítés:** 2024.08.24  
**Verzió:** 1.0

_Ez a dokumentum a Vultaic MGM rendszer konfigurációs optimalizálását mutatja be._
