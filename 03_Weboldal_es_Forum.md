# Vultaic MGM - Weboldal és Fórum Telepítési Útmutató

## Tartalomjegyzék
1. [<PERSON><PERSON><PERSON><PERSON><PERSON>](#<PERSON>ttekintés)
2. [Nginx webszerver beállítása](#nginx-webszerver-beállítása)
3. [PHP konfigurálása](#php-konfigurálása)
4. [SSL tanúsítvány beállítása](#ssl-tanúsítvány-beállítása)
5. [Fórum telepítése (phpBB)](#fórum-telepítése-phpbb)
6. [Invision Power Board (IPB) telepítése](#invision-power-board-ipb-telepítése)
7. [MTA API végpontok létrehozása](#mta-api-végpontok-létrehozása)
8. [Weboldal integráció](#weboldal-integráció)
9. [Statisztika oldal készítése](#statisztika-oldal-készítése)
10. [Biztons<PERSON>gi be<PERSON>](#biztonsági-beállítások)
11. [Hibaelhárítás](#hibaelhárítás)

---

## Áttekintés

A Vultaic MGM rendszer fórum alapú regisztrációt és bejelentkezést használ. A rendszer támogatja:

- **phpBB** - Ingyenes, nyílt forráskódú fórum
- **Invision Power Board (IPB)** - Fizetős, professzionális fórum rendszer

### Szükséges komponensek
- Nginx webszerver
- PHP 8.1+ (FPM)
- MySQL/MariaDB adatbázis
- SSL tanúsítvány (Let's Encrypt)
- Fórum szoftver (phpBB vagy IPB)

### API végpontok
- `mta_login_test.php` - Éles szerver bejelentkezés
- `mta_login_dev.php` - Fejlesztői szerver bejelentkezés
- `mta_getavatar.php` - Avatar lekérés

---

## Nginx webszerver beállítása

### 1. Nginx konfiguráció létrehozása
```bash
sudo nano /etc/nginx/sites-available/vultaic-forum
```

Alapvető konfiguráció:
```nginx
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    root /var/www/vultaic;
    index index.php index.html index.htm;

    # Általános PHP kezelés
    location ~ \.php$ {
        include snippets/fastcgi-php.conf;
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }

    # Fórum könyvtár
    location /forum/ {
        try_files $uri $uri/ /forum/index.php?$query_string;
    }

    # MTA API végpontok
    location ~ ^/(mta_login_test|mta_login_dev|mta_getavatar)\.php$ {
        include snippets/fastcgi-php.conf;
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
        
        # CORS headers MTA számára
        add_header Access-Control-Allow-Origin "*";
        add_header Access-Control-Allow-Methods "POST, GET, OPTIONS";
        add_header Access-Control-Allow-Headers "Content-Type";
    }

    # Biztonsági beállítások
    location ~ /\.ht {
        deny all;
    }

    location ~ /\.git {
        deny all;
    }

    # Statikus fájlok cache
    location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

### 2. Site aktiválása
```bash
# Symlink létrehozása
sudo ln -s /etc/nginx/sites-available/vultaic-forum /etc/nginx/sites-enabled/

# Alapértelmezett site letiltása
sudo rm /etc/nginx/sites-enabled/default

# Konfiguráció tesztelése
sudo nginx -t

# Nginx újraindítása
sudo systemctl restart nginx
```

### 3. Webroot létrehozása
```bash
sudo mkdir -p /var/www/vultaic
sudo chown -R www-data:www-data /var/www/vultaic
sudo chmod -R 755 /var/www/vultaic
```

---

## PHP konfigurálása

### 1. PHP-FPM beállítások
```bash
sudo nano /etc/php/8.1/fpm/php.ini
```

Fontos beállítások:
```ini
; Alapvető beállítások
max_execution_time = 300
max_input_time = 300
memory_limit = 256M
post_max_size = 64M
upload_max_filesize = 64M

; Adatbázis
mysqli.default_host = localhost
mysqli.default_user = mtauser
mysqli.default_pw = MTA_Strong_Password_2024!

; Biztonság
expose_php = Off
allow_url_fopen = On
allow_url_include = Off

; Hibakezelés
display_errors = Off
log_errors = On
error_log = /var/log/php/error.log
```

### 2. PHP-FPM pool konfiguráció
```bash
sudo nano /etc/php/8.1/fpm/pool.d/www.conf
```

Fontos beállítások:
```ini
[www]
user = www-data
group = www-data
listen = /var/run/php/php8.1-fpm.sock
listen.owner = www-data
listen.group = www-data
pm = dynamic
pm.max_children = 50
pm.start_servers = 5
pm.min_spare_servers = 5
pm.max_spare_servers = 35
```

### 3. PHP újraindítása
```bash
sudo systemctl restart php8.1-fpm
sudo systemctl restart nginx
```

---

## SSL tanúsítvány beállítása

### 1. Let's Encrypt telepítése
```bash
sudo apt install certbot python3-certbot-nginx
```

### 2. SSL tanúsítvány megszerzése
```bash
sudo certbot --nginx -d your-domain.com -d www.your-domain.com
```

### 3. Automatikus megújítás
```bash
# Crontab szerkesztése
sudo crontab -e

# Hozzáadandó sor (havonta megújítás)
0 12 * * 0 /usr/bin/certbot renew --quiet
```

---

## Fórum telepítése (phpBB)

### 1. phpBB letöltése
```bash
cd /tmp
wget https://download.phpbb.com/pub/release/3.3/3.3.11/phpBB-3.3.11.tar.bz2
tar -xjf phpBB-3.3.11.tar.bz2
sudo mv phpBB3 /var/www/vultaic/forum
sudo chown -R www-data:www-data /var/www/vultaic/forum
```

### 2. Adatbázis létrehozása fórumhoz
```bash
mysql -u root -p
```

```sql
CREATE DATABASE phpbb_forum CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
GRANT ALL PRIVILEGES ON phpbb_forum.* TO 'mtauser'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

### 3. phpBB telepítése
```bash
# Böngészőben nyisd meg: http://your-domain.com/forum/install/
```

Telepítési adatok:
- **Adatbázis típus:** MySQL with MySQLi Extension
- **Adatbázis szerver:** localhost
- **Adatbázis név:** phpbb_forum
- **Felhasználónév:** mtauser
- **Jelszó:** MTA_Strong_Password_2024!

### 4. Telepítés utáni teendők
```bash
# Install könyvtár törlése
sudo rm -rf /var/www/vultaic/forum/install

# Jogosultságok beállítása
sudo chmod 644 /var/www/vultaic/forum/config.php
```

---

## Invision Power Board (IPB) telepítése

### 1. IPB letöltése és feltöltése
```bash
# IPB fájlok feltöltése (licenc szükséges)
sudo unzip ipb_*.zip -d /var/www/vultaic/forum/
sudo chown -R www-data:www-data /var/www/vultaic/forum/
```

### 2. IPB adatbázis
```sql
CREATE DATABASE ipb_forum CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
GRANT ALL PRIVILEGES ON ipb_forum.* TO 'mtauser'@'localhost';
FLUSH PRIVILEGES;
```

### 3. IPB telepítése
```bash
# Böngészőben: http://your-domain.com/forum/
# Kövesd a telepítő utasításait
```

---

## MTA API végpontok létrehozása

### 1. Login API végpont (mta_login_test.php)
```bash
sudo nano /var/www/vultaic/mta_login_test.php
```

```php
<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Csak POST kérések engedélyezése
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 1, 'message' => 'Method not allowed']);
    exit;
}

// JSON input olvasása
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['username']) || !isset($input['password'])) {
    echo json_encode(['error' => 1, 'message' => 'Invalid input']);
    exit;
}

$username = trim($input['username']);
$password = $input['password'];

// Adatbázis kapcsolat
$mysqli = new mysqli('localhost', 'mtauser', 'MTA_Strong_Password_2024!', 'phpbb_forum');

if ($mysqli->connect_error) {
    echo json_encode(['error' => 1, 'message' => 'Database connection failed']);
    exit;
}

// Felhasználó keresése (phpBB esetén)
$stmt = $mysqli->prepare("SELECT user_id, username, user_password FROM phpbb_users WHERE username = ? AND user_type IN (0, 3)");
$stmt->bind_param("s", $username);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    echo json_encode(['error' => 1, 'message' => 1]); // Account not found
    exit;
}

$user = $result->fetch_assoc();

// Jelszó ellenőrzése (phpBB hash)
if (!password_verify($password, $user['user_password'])) {
    echo json_encode(['error' => 1, 'message' => 2]); // Wrong password
    exit;
}

// Sikeres bejelentkezés
echo json_encode([
    'error' => 0,
    'connect_id' => $user['user_id'],
    'username' => $user['username'],
    'donator' => false // Alapértelmezetten nem donator
]);

$mysqli->close();
?>
```

### 2. Fejlesztői login API (mta_login_dev.php)
```bash
sudo cp /var/www/vultaic/mta_login_test.php /var/www/vultaic/mta_login_dev.php

# Szerkeszd a dev verziót, ha szükséges (pl. más adatbázis)
```

### 3. Avatar API végpont (mta_getavatar.php)
```bash
sudo nano /var/www/vultaic/mta_getavatar.php
```

```php
<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');

$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['username'])) {
    echo json_encode(['error' => 1, 'message' => 'Invalid input']);
    exit;
}

$username = trim($input['username']);

// Adatbázis kapcsolat
$mysqli = new mysqli('localhost', 'mtauser', 'MTA_Strong_Password_2024!', 'phpbb_forum');

if ($mysqli->connect_error) {
    echo json_encode(['error' => 1, 'message' => 'Database connection failed']);
    exit;
}

// Avatar keresése (phpBB esetén)
$stmt = $mysqli->prepare("SELECT user_avatar FROM phpbb_users WHERE username = ?");
$stmt->bind_param("s", $username);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    echo json_encode(['error' => 1, 'message' => 'User not found']);
    exit;
}

$user = $result->fetch_assoc();
$avatar = $user['user_avatar'];

if (empty($avatar)) {
    echo json_encode(['error' => 1, 'message' => 'No avatar']);
    exit;
}

echo json_encode([
    'error' => 0,
    'avatar' => $avatar
]);

$mysqli->close();
?>
```

### 4. Jogosultságok beállítása
```bash
sudo chown www-data:www-data /var/www/vultaic/*.php
sudo chmod 644 /var/www/vultaic/*.php
```

---

## Weboldal integráció

### 1. Főoldal létrehozása (index.php)
```bash
sudo nano /var/www/vultaic/index.php
```

```php
<!DOCTYPE html>
<html lang="hu">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vultaic MGM Server</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="#">Vultaic MGM</a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/forum">Fórum</a>
                <a class="nav-link" href="/stats">Statisztikák</a>
            </div>
        </div>
    </nav>

    <div class="container mt-5">
        <div class="row">
            <div class="col-md-8">
                <h1>Üdvözöl a Vultaic MGM Szerver!</h1>
                <p>Csatlakozz szerverünkhöz és élvezd a legjobb MTA élményt!</p>
                
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Szerver információk</h5>
                        <p><strong>IP:</strong> your-server-ip:22003</p>
                        <p><strong>Játékosok:</strong> <span id="playerCount">Betöltés...</span></p>
                        <p><strong>Játékmódok:</strong> Race, DM, Shooter, Hunter</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Regisztráció</h5>
                        <p>A játékhoz regisztrálj a fórumon!</p>
                        <a href="/forum" class="btn btn-primary">Regisztráció</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
```

### 2. CSS stílusok
```bash
sudo mkdir -p /var/www/vultaic/assets/css
sudo nano /var/www/vultaic/assets/css/style.css
```

```css
/* Egyedi stílusok */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.card {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border: none;
    margin-bottom: 20px;
}

.stats-table {
    background: white;
    border-radius: 8px;
}

.player-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
}
```

---

## Statisztika oldal készítése

### 1. Statisztika oldal (stats.php)
```bash
sudo nano /var/www/vultaic/stats.php
```

```php
<?php
// Adatbázis kapcsolat
$mysqli = new mysqli('localhost', 'mtauser', 'MTA_Strong_Password_2024!', 'v_accounts');

if ($mysqli->connect_error) {
    die('Adatbázis kapcsolat hiba: ' . $mysqli->connect_error);
}

// Top játékosok lekérése
$topPlayers = $mysqli->query("
    SELECT username, money, race_points, dm_points, shooter_points 
    FROM vmtasa_accounts 
    ORDER BY money DESC 
    LIMIT 10
");
?>

<!DOCTYPE html>
<html lang="hu">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Statisztikák - Vultaic MGM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">Vultaic MGM</a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/forum">Fórum</a>
                <a class="nav-link active" href="/stats">Statisztikák</a>
            </div>
        </div>
    </nav>

    <div class="container mt-5">
        <h1>Játékos Statisztikák</h1>
        
        <div class="card">
            <div class="card-header">
                <h5>Top 10 Leggazdagabb Játékos</h5>
            </div>
            <div class="card-body">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Helyezés</th>
                            <th>Játékos</th>
                            <th>Pénz</th>
                            <th>Race pontok</th>
                            <th>DM pontok</th>
                            <th>Shooter pontok</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php 
                        $rank = 1;
                        while ($player = $topPlayers->fetch_assoc()): 
                        ?>
                        <tr>
                            <td><?= $rank++ ?></td>
                            <td><?= htmlspecialchars($player['username']) ?></td>
                            <td>$<?= number_format($player['money']) ?></td>
                            <td><?= number_format($player['race_points']) ?></td>
                            <td><?= number_format($player['dm_points']) ?></td>
                            <td><?= number_format($player['shooter_points']) ?></td>
                        </tr>
                        <?php endwhile; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

<?php $mysqli->close(); ?>
```

---

## Biztonsági beállítások

### 1. PHP biztonsági beállítások
```bash
sudo nano /var/www/vultaic/.htaccess
```

```apache
# Biztonsági fejlécek
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"

# PHP fájlok védelme
<Files "*.php">
    Order allow,deny
    Allow from all
</Files>

# Konfigurációs fájlok védelme
<Files "config.php">
    Order deny,allow
    Deny from all
</Files>

# Logfájlok védelme
<Files "*.log">
    Order deny,allow
    Deny from all
</Files>
```

### 2. Nginx biztonsági fejlécek
```bash
sudo nano /etc/nginx/sites-available/vultaic-forum
```

Hozzáadandó a server blokkhoz:
```nginx
# Biztonsági fejlécek
add_header X-Frame-Options "SAMEORIGIN" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header X-Content-Type-Options "nosniff" always;
add_header Referrer-Policy "no-referrer-when-downgrade" always;
add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

# Rate limiting
limit_req_zone $binary_remote_addr zone=api:10m rate=10r/m;

location ~ ^/(mta_login_test|mta_login_dev)\.php$ {
    limit_req zone=api burst=5 nodelay;
    # ... többi konfiguráció
}
```

---

## Hibaelhárítás

### Gyakori problémák

#### 1. 502 Bad Gateway
```bash
# PHP-FPM státusz ellenőrzése
sudo systemctl status php8.1-fpm

# Nginx error log
sudo tail -f /var/log/nginx/error.log
```

#### 2. API végpontok nem működnek
```bash
# PHP hibák ellenőrzése
sudo tail -f /var/log/php8.1-fpm.log

# Jogosultságok ellenőrzése
ls -la /var/www/vultaic/*.php
```

#### 3. Adatbázis kapcsolat hiba
```bash
# MySQL kapcsolat tesztelése
mysql -u mtauser -p phpbb_forum

# PHP MySQL extension ellenőrzése
php -m | grep mysqli
```

---

## Következő lépések

A weboldal és fórum telepítése után folytasd:
- **04_Konfiguracio_es_Teszteles.md** - Végső konfiguráció és tesztelés

---

*Ez az útmutató a Vultaic MGM rendszer weboldal és fórum integrációját mutatja be. A biztonsági beállítások kritikus fontosságúak!*
