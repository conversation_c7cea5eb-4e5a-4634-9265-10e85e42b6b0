--[[
	VULTAIC MGM - FADE RENDSZER KLIENS OLDAL

	Ez a fájl a fade (elhalványítás) rendszer kliens oldali logikáját tartalmazza.

	Főbb funkciók:
	- Képernyő elhalványítás shader alapon
	- Smooth átmenetek interpolációval
	- Progress alapú vezérlés (0-1 skála)
	- Optimalizált rendering
	- Screen source alapú effekt

	Működés:
	1. Screen source létrehozása a teljes képernyőről
	2. Dark shader alkalmazása az átlátszóság vezérlésével
	3. Interpoláció a smooth átmenetekhez
	4. Event alapú vezérlés

	Progress értékek:
	- 0: Teljes elhalványítás indítása
	- 1: Elhalványítás megszüntetése
	- 0-1 közötti: Részleges elhalványítás
]]

-- Képernyő méret és screen source
local screenWidth, screenHeight = guiGetScreenSize()
local screenSource = dxCreateScreenSource(screenWidth, screenHeight)

-- Optimalizációs változók (lokális referenciák)
local tocolor = tocolor
local math_min = math.min
local math_max = math.max
local math_floor = math.floor
local interpolateBetween = interpolateBetween

--[[
	RESOURCE INDÍTÁS KEZELÉSE

	Dark shader betöltése az elhalványítás effekthez.
]]
addEventHandler("onClientResourceStart", resourceRoot,
function()
	darkShader = dxCreateShader("fx/dark.fx")
end)

--[[
	Progress beállítása

	@param number newProgress - Az új progress érték (0-1)

	Ez a függvény vezérli az elhalványítás mértékét:
	- 0: Teljes elhalványítás indítása
	- 1: Elhalványítás megszüntetése
	- Egyéb: Részleges elhalványítás
]]
function setProgress(newProgress)
	newProgress = tonumber(newProgress) or nil
	if newProgress then
		if newProgress == 0 then
			-- Teljes elhalványítás indítása
			effectTick = getTickCount()
			progress = 1
			progressToGo = 1
			removeEventHandler("onClientRender", root, renderEffect)
			addEventHandler("onClientRender", root, renderEffect, true, "high+5")
		elseif newProgress == 1 then
			-- Elhalványítás megszüntetése
			effectTick = getTickCount()
			progressToGo = 0
		else
			-- Részleges elhalványítás
			effectTick = getTickCount()
			progressToGo = (1 - newProgress)
		end
	else
		-- Alapértelmezett: elhalványítás megszüntetése
		effectTick = getTickCount()
		progressToGo = 0
	end
end
addEvent("fade:setProgress", true)
addEventHandler("fade:setProgress", localPlayer, setProgress)

function renderEffect()
	local currentTick = getTickCount()
	local tick = effectTick or 0
	progress = interpolateBetween(progress or 1, 0, 0, progressToGo or 1, 0, 0, math_min(2000, currentTick - tick)/2000, "Linear")
	if progressToGo == 0 and progress == 0 then
		removeEventHandler("onClientRender", root, renderEffect)
		return
	end
	dxUpdateScreenSource(screenSource)
    dxSetShaderValue(darkShader, "ScreenSource", screenSource)
    dxSetShaderValue(darkShader, "Alpha", progress * 0.5)
    dxDrawImage(0, 0, screenWidth, screenHeight, darkShader, 0, 0, 0, tocolor(255, 255, 255, 255), false)
end