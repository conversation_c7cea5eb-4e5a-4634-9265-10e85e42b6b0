--[[
	VULTAIC MGM - MUSIC RENDSZER KLIENS OLDAL

	Ez a fájl a music (zene) rendszer kliens oldali logikáját tartalmazza.

	Főbb funkciók:
	- Online rádió lej<PERSON>zás
	- Live stream lejátszás
	- Metaadat kezel<PERSON> (dal címek)
	- Automatikus újracsatlakozás
	- Hang típus kezelés
	- Értesítések új dalokról

	Támogatott források:
	- Energy98 rádió állo<PERSON>ás
	- Egyedi stream szerver

	Működés:
	1. Játékos beállítása alapján rádió/stream indítása
	2. Metaadat figyelés (dal címek)
	3. Értesítések megjelenítése új dalokról
	4. Automatikus kezelés resource újraindításkor
]]

-- Rádió és stream URL-ek
local stationURL = "http://www.energy981.com/playlist/Energy98_128WM.asx"  -- Energy98 rádió
local streamURL = "http://57.129.69.113:8000/stream"                    -- Egyedi stream

--[[
	<PERSON><PERSON><PERSON><PERSON> lej<PERSON>z<PERSON> indítása

	Energy98 rádió állomás lejátszása metaadat figyeléssel.
]]
function playRadio()
	-- Ellenőrzés, hogy már fut-e
	if isElement(radioSound) then
		return
	end

	-- Rádió indítása
	radioSound = playSound(stationURL)
	setElementData(radioSound, "sound_type", "radio", false)
	addEventHandler("onClientSoundChangedMeta", radioSound, getSoundMeta)
end

--[[
	Rádió lejátszás leállítása

	Rádió leállítása és event handler eltávolítása.
]]
function stopRadio()
	if isElement(radioSound) then
		removeEventHandler("onClientSoundChangedMeta", radioSound, getSoundMeta)
		destroyElement(radioSound)
	end
end

--[[
	Stream lejátszás indítása

	Egyedi stream szerver lejátszása metaadat figyeléssel.
]]
function playStream()
	-- Ellenőrzés, hogy már fut-e
	if isElement(radioSound) then
		return
	end

	-- Stream indítása
	streamSound = playSound(streamURL)
	setElementData(streamSound, "sound_type", "stream", false)
	addEventHandler("onClientSoundChangedMeta", streamSound, getSoundMeta)
end

--[[
	Stream lejátszás leállítása

	Stream leállítása és event handler eltávolítása.
]]
function stopStream()
	if isElement(streamSound) then
		removeEventHandler("onClientSoundChangedMeta", streamSound, getSoundMeta)
		destroyElement(streamSound)
	end
end

--[[
	RESOURCE INDÍTÁS KEZELÉSE

	Resource indításkor a játékos beállítása alapján
	automatikusan elindítja a megfelelő audio forrást.
]]
addEventHandler("onClientResourceStart", resourceRoot,
function()
	local soundMode = getElementData(localPlayer, "sound_mode")

	if soundMode == "radio" then
		playRadio()
	elseif soundMode == "stream" then
		playStream()
	end
end)

function getSoundMeta(streamTitle)
	local meta = getSoundMetaTags(source)
	if previousStreamTitle and previousStreamTitle == streamTitle then
		return
	end
	iprint(meta)
	local stream_title = getElementData(localPlayer, "sound_mode") == "radio" and "Radio" or "Streamer: "..tostring(meta.stream_name or "None")
	if streamTitle then
		streamTitle = streamTitle:gsub(".mp3", "")
		previousStreamTitle = streamTitle
		triggerEvent("notification:create", localPlayer, stream_title, "Playing "..streamTitle)
	end
end

addEventHandler("onClientElementDataChange", localPlayer,
function(dataName)
	if dataName ~= "sound_mode" then
		return
	end
	if getElementData(localPlayer, "sound_mode") == "radio" then
		playRadio()
	else
		stopRadio()
	end
	if getElementData(localPlayer, "sound_mode") == "stream" then
		playStream()
	else
		stopStream()
	end
end)

addCommandHandler("livestream", function(command, ...)
	local stream_token, streamer = arg[1], arg[2] or getPlayerName(localPlayer)
	if not stream_token then
		return outputChatBox("* Error: Provide a valid stream title!", 255, 0, 0)
	end
	if not isElement(streamSound) then
		return outputChatBox("* Error: Server is not receiving any live stream data", 255, 0, 0)
	end
	local meta = getSoundMetaTags(streamSound)
	iprint(meta)
	if not meta.stream_name then
		return outputChatBox("* Error: No meta data for stream", 255, 0, 0)
	end
	if not meta.stream_name:find(stream_token, 1, true) --[[meta.stream_name ~= stream_token]] then
		return outputChatBox("* Error: Invalid Stream token", 255, 0, 0)
	end
	triggerServerEvent("Music:onStreamVerify", resourceRoot, streamer)
end)