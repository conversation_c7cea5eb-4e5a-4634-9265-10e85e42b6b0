# Vultaic MGM – Telepítési és Élesítési Checklist

Ez a checklist seg<PERSON><PERSON> a<PERSON>, hogy a Vultaic MGM szervered fejlesztői és éles környezetben is biz<PERSON>ágosan, á<PERSON><PERSON>thatóan és hibamentesen működjön.

---

## 1. Alapvető szerverfájlok ellenőrzése
- [ ] Friss, letesztelt resource-ok (core, admin, admin2, acpanel, ipb, runcode, v_admin, stb.)
- [ ] Frissített `mtaserver.conf` vagy `vultaic_mtaserver_minimal.conf` (portok, szerverjelszó, resource lista)
- [ ] He<PERSON><PERSON>, <PERSON>les `acl.xml` (jogosultságok, resource-ok)
- [ ] Frissített `meta.xml` fájlok a fő resource-okban
- [ ] Nincsenek duplikált resource indítások

## 2. Adatbázis be<PERSON>llítások
- [ ] `v_mysql/mysql_server.lua` he<PERSON><PERSON> (host, user, password, db, port)
- [ ] <PERSON><PERSON> környezetben külön adatbázis, külön felhasználóval (jogosultságok limitálva)
- [ ] Fejlesztői és éles adatbázis szétválasztva
- [ ] Adatbázis backup rendszer beállítva (pl. automatikus mentés naponta)

## 3. Jogosultságok, ACL, adminisztráció
- [ ] `acl.xml`-ben csak a szükséges csoportok, jogok, resource-ok
- [ ] Admin jogosultságok kiosztása csak megbízható accountoknak
- [ ] Nincs hardcoded serial/IP/jelszó a resource-okban
- [ ] Runcode/ipb/admin2 modulok külön auditálva
- [ ] Szerverlogok figyelése: gyanús parancs, hibák, jogosultságproblémák

## 4. Szerver konfiguráció, biztonság
- [ ] Minden port, jelszó, titkos adat csak a konfigban szerepel
- [ ] `database_credentials_protection` aktív
- [ ] `backup_path`, `backup_interval`, `backup_copies` beállítva
- [ ] Debug/teszt resource-ok NEM indulnak élesben
- [ ] Szerver logok, backup mappák jogosultságai helyesek

## 5. Fórum/web integráció (ha van)
- [ ] IPB/fórum integrációhoz szükséges resource-ok, kulcsok, endpointok beállítva
- [ ] Fórum alapú regisztráció/auth tesztelve

## 6. Indítás, tesztelés
- [ ] Szerver indítása, logok ellenőrzése
- [ ] Hibák, warningok javítása
- [ ] Admin funkciók, játékos belépés, adatbázis műveletek tesztelése

## 7. Mentések, visszaállítás
- [ ] Rendszeres backup tesztelve (adatbázis, konfig, resource-ok)
- [ ] Backup visszaállítási folyamat dokumentálva

---

## Pro Tipp
- Minden változtatás előtt készíts mentést!
- A checklistet minden élesítés előtt futtasd végig!
- A szerver logokat folyamatosan figyeld az első napokban!

---

Ha bármilyen kérdésed van, vagy speciális workflow-t szeretnél dokumentálni, egészítsd ki ezt a checklistet saját igényeid szerint!
