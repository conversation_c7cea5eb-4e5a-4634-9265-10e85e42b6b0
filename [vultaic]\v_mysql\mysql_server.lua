--[[
	VULTAIC MGM - MYSQL ADATBÁZIS RENDSZER SZERVER OLDAL

	Ez a fájl a MySQL adatbázis kapcsolat és felhasználói fiók kezelés központi rendszere.

	Főbb funkciók:
	- MySQL adatbázis kapcsolat kezelése
	- Felhasználói fiókok kezelése (CAccount osztály)
	- Játékos statisztikák tárolása és lekérdezése
	- Játékidő nyilvántartás
	- Login/logout kezelés
	- Development/Production mód támogatás

	Adatbázis struktúra:
	- v_accounts_dev: fejlesztői adatbázis
	- v_accounts: éles adatbázis

	Kapcsolódó resource-ok:
	- v_login: bejelentkezési rendszer
	- v_awards: achievement rendszer
]]

-- Fejlesztői mód beállítása
local DEVELOPMENT_MODE = true

-- <PERSON>tb<PERSON><PERSON>s név meghatározása port alapján
-- Ha nem a 22003-as porton fut ÉS fejlesztői mód aktív, akkor dev adatbázist használ
local DB_NAME = (getServerPort() ~= 22003 and DEVELOPMENT_MODE) and "v_accounts_dev" or "v_accounts"

--[[ GLOBÁLIS VÁLTOZÓK ]]--
g_Connection = nil      -- MySQL kapcsolat objektum
g_Userdata = {}        -- Felhasználói adatok cache

--[[ EVENT KEZELŐK ÉS CALLBACK FÜGGVÉNYEK ]]--

--[[
	Script betöltés kezelése

	Ez a függvény fut le, amikor a MySQL resource elindul:
	1. Elindítja/újraindítja a v_login resource-t
	2. Elindítja a v_awards resource-t
	3. Létrehozza a MySQL kapcsolatot
	4. Visszaállítja a globális login állapotokat
]]
function onScriptLoad()
	-- Login resource kezelése
	local v_login = getResourceFromName("v_login")
	if getResourceState(v_login) == "running" then
		restartResource(v_login)    -- Újraindítás ha már fut
	else
		startResource(v_login)      -- Indítás ha nem fut
	end

	-- Awards (achievement) resource indítása
	startResource(getResourceFromName("v_awards"))
	valid_awards = exports.v_awards:getAwards()  -- Érvényes achievementek betöltése

	-- MySQL kapcsolat létrehozása
	g_Connection, error = dbConnect("mysql", "dbname="..DB_NAME..";host=127.0.0.1;port=3306", "sunuser", "SunUser2025@&#")

	if g_Connection then
		resetGlobalLoginStatus()    -- Login állapotok visszaállítása
		outputDebugString("MySQL: Connected")
	else
		outputDebugString("MySQL: Failed to connect")
	end
end
addEventHandler("onResourceStart", resourceRoot, onScriptLoad)

--[[
	Script leállítás kezelése

	Ez a függvény fut le, amikor a MySQL resource leáll:
	1. Minden játékos account objektumát megsemmisíti
	2. Lezárja a MySQL kapcsolatot
	3. Leállítja a v_login resource-t
]]
function onScriptUnload()
	-- Minden játékos account objektumának megsemmisítése
	for _, p in pairs(getElementsByType("player")) do
		CAccount.destructor(p)
	end

	-- MySQL kapcsolat lezárása
	destroyElement(g_Connection)

	-- Login resource leállítása
	stopResource(getResourceFromName("v_login"))

	outputDebugString("MySQL: Disconnected")
end
addEventHandler("onResourceStop", resourceRoot, onScriptUnload)

addEvent("login:onPlayerLogin", true)
addEventHandler("login:onPlayerLogin", root, function(userdata)
	CAccount:new(source, userdata)
end)

addEvent("mysql:onRequestPlayerStats", true)
addEventHandler("mysql:onRequestPlayerStats", root, function(player)
	local userdata = getElementData(player, "LoggedIn") and g_Userdata[player] or false
	-- Update playtime
	if getElementData(player, "arena") ~= "lobby" then
		local joinTick = getPlayerStats(player, "PlayTimeTick") or getTickCount()
		local playTime = getTickCount()-joinTick
		local current_playtime = getPlayerStats(player, "playtime") or 0
		setPlayerStats(player, "playtime", current_playtime+playTime)
		setPlayerStats(player, "PlayTimeTick", getTickCount(), true)
	end
	--
	triggerClientEvent(client, "mysql:onReceivePlayerStats", client, player, userdata)
end)

function resetGlobalLoginStatus()
	for _, p in pairs(getElementsByType("player")) do
		setElementData(p, "account_id", nil)
		setElementData(p, "username", nil)
		setElementData(p, "LoggedIn", false)
	end
end

-- Statistics
addEvent("core:onPlayerJoinArena", true)
addEventHandler("core:onPlayerJoinArena", root, function(arena)
	setPlayerStats(source, "PlayTimeTick", getTickCount(), true)
end)

addEvent("core:onPlayerLeaveArena", true)
addEventHandler("core:onPlayerLeaveArena", root, function(arena)
	local joinTick = getPlayerStats(source, "PlayTimeTick") or getTickCount()
	local playTime = getTickCount()-joinTick
	local current_playtime = getPlayerStats(source, "playtime") or 0
	setPlayerStats(source, "playtime", current_playtime+playTime)
end)

-- Exported functions
function getMySQLConnectionPointer()
	return class_Accounts.connection or false
end

function setPlayerStats(...)
	CAccount:setPlayerStats(...)
end

function getPlayerStats(...)
	return CAccount:getPlayerStats(...)
end

function takePlayerStats(...)
	CAccount:takePlayerStats(...)
end

function givePlayerStats(...)
	CAccount:givePlayerStats(...)
end

function setPlayerTuningStats(...)
	CAccount:setPlayerTuningStats(...)
end

function getPlayerTuningStats(...)
	return CAccount:getPlayerTuningStats(...)
end

function givePlayerAward(...)
	return CAccount:givePlayerAward(...)
end