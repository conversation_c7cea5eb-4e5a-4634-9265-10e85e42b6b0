# Vultaic MGM - Szerver Indítási Hibák Elhárítása

## 🚨 Jelenlegi Problémák és Megoldások

### 1. **MySQL Kapcsolat Hiba** ⚠️
```
WARNING: Bad usage @ 'dbConnect' [Access denied for user 'root'@'localhost']
```

**Probléma:** A resource-ok még mindig `root` felhasználót próbálnak használni.

**Megoldás:**
```bash
# 1. Ellenőrizd a MySQL felhasználót és adatbázisokat
mysql -u root -p < mysql_setup_check.sql

# 2. <PERSON>, hozd létre a sunuser felhasználót:
mysql -u root -p
```

```sql
CREATE USER IF NOT EXISTS 'sunuser'@'localhost' IDENTIFIED BY 'SunUser2025@&#';
GRANT ALL PRIVILEGES ON v_accounts.* TO 'sunuser'@'localhost';
GRANT ALL PRIVILEGES ON v_accounts_dev.* TO 'sunuser'@'localhost';
GRANT ALL PRIVILEGES ON mta_toptimes.* TO 'sunuser'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

### 2. **Resource Függőségi Problémák** 🔗
```
ERROR: exports: Call to non-running server resource (v_admin)
```

**Probléma:** A `v_admin` resource nem indul el elég korán.

**Megoldás:** ✅ **JAVÍTVA** - A `mtaserver.conf`-ban a `v_admin` most már első prioritással indul.

### 3. **ACL Jogosultság Problémák** 🛡️
```
WARNING: Access denied @ 'aclSetRight'
```

**Probléma:** Az admin resource-ok nem tudják módosítani az ACL-t.

**Megoldás:** ✅ **JAVÍTVA** - A Console csoport most már Admin jogosultságokkal rendelkezik.

---

## 🔧 Javított Konfiguráció

### 1. **mtaserver.conf - Optimalizált Resource Sorrend**
```xml
<!-- 1. ADMIN RENDSZER (ELSŐ PRIORITÁS) -->
<resource src="v_admin" startup="1" protected="0" />
<resource src="admin" startup="1" protected="0" />
<resource src="admin2" startup="1" protected="0" />
<resource src="acpanel" startup="1" protected="0" />
<resource src="ipb" startup="1" protected="0" />
<resource src="runcode" startup="1" protected="0" />

<!-- 2. SZINKRONIZÁCIÓS MODULOK -->
<resource src="v_utils" startup="1" protected="0" />

<!-- 3. FŐ CORE RESOURCE -->
<resource src="core" startup="1" protected="0" />
```

### 2. **ACL.xml - Console Jogosultságok**
```xml
<group name="Console">
    <acl name="RPC" />
    <acl name="Admin" />
    <object name="user.Console" />
</group>
```

### 3. **MySQL Kapcsolatok - Frissített Adatok**
Minden resource most már `sunuser` és `SunUser2025@&#` adatokat használ:
- ✅ `v_mysql/mysql_server.lua`
- ✅ `v_clans/clan_server.lua`
- ✅ `v_achievements/achievements_s.lua`
- ✅ `v_toptimes/toptimes_server.lua`

---

## 🚀 Szerver Újraindítási Lépések

### 1. **MySQL Ellenőrzés**
```bash
# Kapcsolat teszt
mysql -u sunuser -p
# Jelszó: SunUser2025@&#

# Adatbázisok ellenőrzése
SHOW DATABASES;
USE v_accounts;
SHOW TABLES;
```

### 2. **MTA Szerver Újraindítás**
```bash
# Ha systemd service-t használsz:
sudo systemctl restart mta-server

# Vagy manuálisan:
cd /opt/mta-sun
sudo -u mtaserver ./mta-server64
```

### 3. **Logok Ellenőrzése**
```bash
# Szerver log
tail -f /opt/mta-sun/mods/deathmatch/logs/server.log

# Script logok
tail -f /opt/mta-sun/mods/deathmatch/logs/scripts.log

# MySQL kapcsolatok keresése
grep -i "mysql.*connected" /opt/mta-sun/mods/deathmatch/logs/scripts.log
```

---

## ✅ Elvárt Eredmények

### Sikeres Indítás Után:
```
[25-08-25 18:06:30] Resources: 66 loaded, 0 failed
[25-08-25 18:06:30] Starting resources...
[25-08-25 18:06:30] INFO: MySQL: Connected
[25-08-25 18:06:30] INFO: Achievements MySQL: Connected
[25-08-25 18:06:30] [vultaic]/v_clans/clan_server.lua:39: Clans: Connected
[25-08-25 18:06:30] Server started and is ready to accept connections!
```

### Resource Állapotok:
```bash
# MTA konzolban:
list

# Keresendő resource-ok [running] státusszal:
v_admin [running]
v_mysql [running]
v_login [running]
v_achievements [running]
v_clans [running]
core [running]
```

---

## 🐛 További Hibaelhárítás

### Ha még mindig MySQL hiba van:

#### 1. **Felhasználó létezésének ellenőrzése**
```sql
mysql -u root -p
SELECT User, Host FROM mysql.user WHERE User = 'sunuser';
```

#### 2. **Jogosultságok ellenőrzése**
```sql
SHOW GRANTS FOR 'sunuser'@'localhost';
```

#### 3. **Adatbázis létezésének ellenőrzése**
```sql
SHOW DATABASES LIKE 'v_%';
```

### Ha resource hibák vannak:

#### 1. **Resource jogosultságok**
```bash
sudo chown -R mtaserver:mtaserver /opt/mta-sun/mods/deathmatch/resources/
```

#### 2. **Meta.xml szintaxis ellenőrzés**
```bash
find /opt/mta-sun/mods/deathmatch/resources/ -name "meta.xml" -exec xmllint {} \;
```

#### 3. **Resource újraindítás**
```bash
# MTA konzolban:
restart v_admin
restart v_mysql
restart core
```

---

## 📋 Ellenőrzési Lista

### Szerver Indítás Előtt:
- [ ] **MySQL sunuser létezik** - `mysql -u sunuser -p`
- [ ] **Adatbázisok léteznek** - `v_accounts`, `v_accounts_dev`, `mta_toptimes`
- [ ] **vmtasa_accounts tábla létezik** - Mindkét adatbázisban
- [ ] **ACL.xml szintaxis helyes** - `xmllint acl.xml`
- [ ] **mtaserver.conf szintaxis helyes** - `xmllint mtaserver.conf`

### Szerver Indítás Után:
- [ ] **Nincs MySQL kapcsolat hiba** - Logokban
- [ ] **v_admin resource fut** - `list` parancs
- [ ] **Nincs "exports: Call to non-running" hiba** - Logokban
- [ ] **Admin jogosultságok működnek** - `/aclrequest` parancs

---

## 🎯 Következő Lépések

1. **Indítsd újra a szervert** a javított konfigurációval
2. **Ellenőrizd a logokat** MySQL kapcsolat hibákért
3. **Teszteld az admin parancsokat** bejelentkezés után
4. **Ha minden működik**, folytasd a weboldal és fórum beállítással

---

*Ez az útmutató a jelenlegi szerver indítási problémák megoldását mutatja be. A javítások után a szerver hibamentesen kell, hogy elinduljon.*
