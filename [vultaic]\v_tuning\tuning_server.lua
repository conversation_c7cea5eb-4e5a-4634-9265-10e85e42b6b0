--[[
	VULTAIC MGM - TUNING RENDSZER SZERVER OLDAL

	Ez a fájl a tuning (j<PERSON><PERSON><PERSON> testreszabás) rendszer szerver oldali logikáját tartalmazza.

	Főbb funkciók:
	- Tuning upgrade árak kezelése
	- Exkluzív upgradek kezelése (donator/member)
	- Premium státusz ellenőrzés
	- Kliens-szerver kommunikáció
	- Színkód konverziók
	- MySQL integráció

	Tuning kategóriák és árak:
	- Colors (Színek): 15,000$
	- Tints (Ablakfólia): 50,000$
	- Wheels (Kerekek): 25,000$
	- Body parts (Karosszéria): 200,000$
	- Stickers (Matricák): 50,000$
	- Lights (Világítás): 50,000$
	- Overlays (Rátétek): 50,000$ - Exkluzív
	- Neons (Neon): 50,000$ - Exkluzív
	- Rocket color (Rakéta szín): 25,000$
	- Skins (Kinézetek): 15,000$
]]

-- Tuning upgrade árak definíciója
local upgradePrices = {
	["Colors"] = 15000,        -- J<PERSON>rmű színek
	["Tints"] = 50000,         -- Ablakfólia
	["Wheels"] = 25000,        -- Kerekek (eredeti: 150000)
	["Body parts"] = 200000,   -- Karosszéria részek
	["Stickers"] = 50000,      -- Matricák (eredeti: 150000)
	["Lights"] = 50000,        -- Világítás
	["Overlays"] = 50000,      -- Rátétek (exkluzív)
	["Neons"] = 50000,         -- Neon világítás (exkluzív)
	["Rocket color"] = 25000,  -- Rakéta szín
	["Skins"] = 15000          -- Jármű kinézetek
}

-- Exkluzív upgradek (csak donator/member számára)
local exclusiveUpgrades = {
	["Overlays"] = true,        -- Rátétek
	["Neons"] = true,           -- Neon világítás
	["Dynamic lights"] = true   -- Dinamikus világítás
}

--[[
	Tuning upgradek lekérése

	@return table upgradePrices - Upgrade árak
	@return table exclusiveUpgrades - Exkluzív upgradek

	Ha kliens hívja meg, akkor triggerel egy eventet.
	Ha szerver hívja meg, akkor visszaadja az adatokat.
]]
function getTuningUpgrades()
	if client then
		-- Kliens számára event triggerelése
		triggerClientEvent(client, "onClientReceiveTuningUpgrades", client, upgradePrices, exclusiveUpgrades)
	else
		-- Szerver számára közvetlen visszaadás
		return upgradePrices, exclusiveUpgrades
	end
end
addEvent("getTuningUpgrades", true)
addEventHandler("getTuningUpgrades", root, getTuningUpgrades)

--[[
	Premium státusz ellenőrzése

	@param element player - A játékos element
	@return boolean - True ha donator vagy member

	Ellenőrzi, hogy a játékos rendelkezik-e premium jogosultságokkal.
]]
function getPremiumStatus(player)
	if isElement(player) then
		return getElementData(player, "donator") or getElementData(player, "member") and true or false
	end
end

--[[addEventHandler("onElementModelChange", root,
function(oldModel, newModel)
	if getElementType(source) == "vehicle" then
		local occupant = getVehicleOccupant(source)
		if not isElement(occupant) then
			return
		end
		triggerClientEvent(getElementParent(occupant), "onClientElementModelChange", resourceRoot, source, oldModel, newModel)
	end
end)]]--

function hexToRGB(hex)
	hex = hex:gsub("#", "") 
	return tonumber("0x"..hex:sub(1, 2)) or 255, tonumber("0x"..hex:sub(3, 4)) or 255, tonumber("0x"..hex:sub(5, 6)) or 255
end

setPlayerTuningStats = function(player, stats, value, ...)
	return exports.v_mysql:setPlayerTuningStats(player, stats, value, ...)
end

getPlayerTuningStats = function(player, stats, ...)
	return exports.v_mysql:getPlayerTuningStats(player, stats, ...)
end

takePlayerStats = function(player, stats, ...)
	return exports.v_mysql:takePlayerStats(player, stats, ...)
end