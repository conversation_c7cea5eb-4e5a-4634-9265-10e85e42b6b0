-- DD<PERSON> OMG generated script, PLACE IT SERVER-SIDE

function omg_LOL()
  omg2121 = createObject(1683, 2625, -4555, 13.60000038147, 0, 0, 272.25)
  omgMoveomg2121(1)
  omg8944 = createObject(14553, 2811.2998046875, -4699.599609375, 85.099998474121, 0, 359.74731445313, 247.7467956543)
  omgMoveomg8944(1)
  omg4382 = createObject(10757, 2780.3999023438, -4617.7001953125, 92.199996948242, 0, 0, 266)
  omgMoveomg4382(1)
  omg5590 = createObject(1681, 2566.8999023438, -5022.1000976563, 48.799999237061, 0, 0, 0)
  omgMoveomg5590(1)
  omg8784 = createObject(1681, 2245.5, -4177.599609375, 81.099998474121, 0, 0, 197.49996948242)
  omgMoveomg8784(1)
  omg5008 = createObject(1683, 2602.8999023438, -4604.7998046875, 19.60000038147, 0, 0, 180)
  omgMoveomg5008(1)
  omg2310 = createObject(1332, 2159.3999023438, -4728.7001953125, 8.8000001907349, 0, 0, 35.5)
  omgMoveomg2310(1)
  omg9747 = createObject(1332, 2641, -4372.8999023438, 8.8999996185303, 0, 0, 33.997192382813)
  omgMoveomg9747(1)
  omg6088 = createObject(1683, 2646.1000976563, -4737, 13.699999809265, 0, 0, 181.50003051758)
  omgMoveomg6088(1)
  omg5725 = createObject(14553, 2227.5, -4303.7001953125, 51.299999237061, 0, 358.24392700195, 358.99438476563)
  omgMoveomg5725(1)
end

function omgMoveomg2121(point)
  if point == 1 then
    moveObject(omg2121, 5000, 2625, -4555, 13.60000038147, 0, 0, 0)
    setTimer(omgMoveomg2121, 5000, 1, 2)
  elseif point == 2 then
    moveObject(omg2121, 5000, 2622.6000976563, -4576.3999023438, 14.10000038147, 0, 0.5, -90.5)
    setTimer(omgMoveomg2121, 5000, 1, 3)
  elseif point == 3 then
    moveObject(omg2121, 5000, 2576.8999023438, -4578.7998046875, 14.10000038147, 0, 0, 0)
    setTimer(omgMoveomg2121, 5000, 1, 4)
  elseif point == 4 then
    moveObject(omg2121, 5000, 2495.1000976563, -4577.2998046875, 15.800000190735, 0, -4, 0)
    setTimer(omgMoveomg2121, 5000, 1, 5)
  elseif point == 5 then
    moveObject(omg2121, 5000, 2416.6000976563, -4575.7001953125, 28.799999237061, 0, -6, 0)
    setTimer(omgMoveomg2121, 5000, 1, 6)
  elseif point == 6 then
    moveObject(omg2121, 5000, 1855.9000244141, -4589.2998046875, 59.599998474121, 0, 0, 0)
    setTimer(omgMoveomg2121, 5000, 1, 7)
  elseif point == 7 then
    moveObject(omg2121, 5000, 1833.5999755859, -4646.7001953125, 59.599998474121, 0, 7.25, 68)
    setTimer(omgMoveomg2121, 5000, 1, 8)
  elseif point == 8 then
    moveObject(omg2121, 5000, 1829, -4715.6000976563, 59.599998474121, 0, 0, 111.25)
    setTimer(omgMoveomg2121, 5000, 1, 9)
  elseif point == 9 then
    moveObject(omg2121, 5000, 2738, -4697.8999023438, 59.599998474121, 0, 0, 0)
    setTimer(omgMoveomg2121, 5000, 1, 10)
  elseif point == 10 then
    moveObject(omg2121, 5000, 2738, -4697.8999023438, 59.599998474121, 0, 0, 0)
    setTimer(omgMoveomg2121, 5000, 1, 11)
  elseif point == 11 then
    moveObject(omg2121, 5000, 2742.1999511719, -4600.2001953125, 59.599998474121, 0, 0, 84)
    setTimer(omgMoveomg2121, 5000, 1, 12)
  elseif point == 12 then
    moveObject(omg2121, 5000, 2742.1999511719, -4600.2001953125, 59.599998474121, 0, -5.25, 95.999984741211)
    setTimer(omgMoveomg2121, 5000, 1, 13)
  elseif point == 13 then
    moveObject(omg2121, 5000, 2526.3999023438, -4596.2998046875, 59.599998474121, 0, 0, 0)
    setTimer(omgMoveomg2121, 5000, 1, 14)
  elseif point == 14 then
    moveObject(omg2121, 5000, 2526.8000488281, -4592.7998046875, 59.599998474121, 62.587280273438, 2.2666015625, -28.793197631836)
    setTimer(omgMoveomg2121, 5000, 1, 15)
  elseif point == 15 then
    moveObject(omg2121, 5000, 1929.8000488281, -4320.5, 59.599998474121, 0, 0, 0)
    setTimer(omgMoveomg2121, 5000, 1, 16)
  elseif point == 16 then
    moveObject(omg2121, 5000, 2625, -4555, 13.60000038147, -62.587280273438, 5.2333984375, 120.04321289063)
    setTimer(omgMoveomg2121, 5000, 1, 1)
  end
end

function omgMoveomg8944(point)
  if point == 1 then
    moveObject(omg8944, 5000, 2575.8999023438, -4637.7998046875, 85.099998474121, 0, 10.75, 0)
    setTimer(omgMoveomg8944, 5000, 1, 2)
  elseif point == 2 then
    moveObject(omg8944, 5000, 2226.3999023438, -4616, 85.099998474121, 0, 0, 0)
    setTimer(omgMoveomg8944, 5000, 1, 3)
  elseif point == 3 then
    moveObject(omg8944, 5000, 2038, -4593.8999023438, 85.099998474121, 0, -64.249938964844, -55.499923706055)
    setTimer(omgMoveomg8944, 5000, 1, 4)
  elseif point == 4 then
    moveObject(omg8944, 5000, 2048.6999511719, -4341.2001953125, 85.099998474121, 0, 0, -79.249984741211)
    setTimer(omgMoveomg8944, 5000, 1, 5)
  elseif point == 5 then
    moveObject(omg8944, 5000, 2220.8000488281, -4082.3000488281, 85.099998474121, 0, 0, 0)
    setTimer(omgMoveomg8944, 5000, 1, 6)
  elseif point == 6 then
    moveObject(omg8944, 5000, 2811.2998046875, -4699.599609375, 85.099998474121, 0, 53.499938964844, 134.74990844727)
    setTimer(omgMoveomg8944, 5000, 1, 1)
  end
end

function omgMoveomg4382(point)
  if point == 1 then
    moveObject(omg4382, 5000, 1934.1999511719, -4642.1000976563, 51.200000762939, 0, 0, 0)
    setTimer(omgMoveomg4382, 5000, 1, 2)
  elseif point == 2 then
    moveObject(omg4382, 5000, 2032.0999755859, -4710.7998046875, 27, 0, 0, -178.00006103516)
    setTimer(omgMoveomg4382, 5000, 1, 3)
  elseif point == 3 then
    moveObject(omg4382, 5000, 2139.1000976563, -4708.6000976563, 35.299999237061, -6.1870727539063, 39.524841308594, 13.081481933594)
    setTimer(omgMoveomg4382, 5000, 1, 4)
  elseif point == 4 then
    moveObject(omg4382, 5000, 2722.1999511719, -4576.7001953125, 35.299999237061, -1.6582641601563, -28.167114257813, -3.5109252929688)
    setTimer(omgMoveomg4382, 5000, 1, 5)
  elseif point == 5 then
    moveObject(omg4382, 5000, 2780.3999023438, -4617.7001953125, 92.199996948242, 7.8453369140625, -11.357727050781, 168.42950439453)
    setTimer(omgMoveomg4382, 5000, 1, 1)
  end
end

function omgMoveomg5590(point)
  if point == 1 then
    moveObject(omg5590, 5000, 2571.3999023438, -4872.3999023438, 48.799999237061, 0, 0, 0)
    setTimer(omgMoveomg5590, 5000, 1, 2)
  elseif point == 2 then
    moveObject(omg5590, 5000, 2559.1000976563, -4810.2998046875, 31.299999237061, 0, 0, 0)
    setTimer(omgMoveomg5590, 5000, 1, 3)
  elseif point == 3 then
    moveObject(omg5590, 5000, 2479.8999023438, -4705.5, 31.299999237061, 0, 0, 44)
    setTimer(omgMoveomg5590, 5000, 1, 4)
  elseif point == 4 then
    moveObject(omg5590, 5000, 2466.1999511719, -4595.6000976563, 31.299999237061, 0, 0, 0)
    setTimer(omgMoveomg5590, 5000, 1, 5)
  elseif point == 5 then
    moveObject(omg5590, 5000, 2501.3000488281, -4507.7998046875, 31.299999237061, 0, 0, -60)
    setTimer(omgMoveomg5590, 5000, 1, 6)
  elseif point == 6 then
    moveObject(omg5590, 5000, 2520, -4432.3999023438, 31.299999237061, 0, 0, 0)
    setTimer(omgMoveomg5590, 5000, 1, 7)
  elseif point == 7 then
    moveObject(omg5590, 5000, 2520, -4432.3999023438, 13.300000190735, 0, 0, 86)
    setTimer(omgMoveomg5590, 5000, 1, 8)
  elseif point == 8 then
    moveObject(omg5590, 5000, 2376.6000976563, -4380.3999023438, 13.300000190735, 0, 0, 0)
    setTimer(omgMoveomg5590, 5000, 1, 9)
  elseif point == 9 then
    moveObject(omg5590, 5000, 2376.6000976563, -4380.3999023438, 13.300000190735, 0, 0, 78.000030517578)
    setTimer(omgMoveomg5590, 5000, 1, 10)
  elseif point == 10 then
    moveObject(omg5590, 5000, 2314.6000976563, -4503.7001953125, 13.300000190735, 0, 0, 0)
    setTimer(omgMoveomg5590, 5000, 1, 11)
  elseif point == 11 then
    moveObject(omg5590, 5000, 2275.1999511719, -4570.8999023438, 13.300000190735, 0, -24, 19.999969482422)
    setTimer(omgMoveomg5590, 5000, 1, 12)
  elseif point == 12 then
    moveObject(omg5590, 5000, 2211, -4680.3999023438, 16, 0, 52, 2)
    setTimer(omgMoveomg5590, 5000, 1, 13)
  elseif point == 13 then
    moveObject(omg5590, 5000, 2182.3999023438, -4783.7001953125, 22.799999237061, 0, -113.99996948242, 8.25)
    setTimer(omgMoveomg5590, 5000, 1, 14)
  elseif point == 14 then
    moveObject(omg5590, 5000, 2186.1000976563, -4979.5, 22.799999237061, 0, 0, 0)
    setTimer(omgMoveomg5590, 5000, 1, 15)
  elseif point == 15 then
    moveObject(omg5590, 5000, 2566.8999023438, -5022.1000976563, 48.799999237061, 0, 85.999969482422, -178.25)
    setTimer(omgMoveomg5590, 5000, 1, 1)
  end
end

function omgMoveomg8784(point)
  if point == 1 then
    moveObject(omg8784, 5000, 2232.3999023438, -4455.7998046875, 81.099998474121, 0, 0, 0)
    setTimer(omgMoveomg8784, 5000, 1, 2)
  elseif point == 2 then
    moveObject(omg8784, 5000, 2441.5, -5016.3999023438, 81.099998474121, 0, 0, 0)
    setTimer(omgMoveomg8784, 5000, 1, 3)
  elseif point == 3 then
    moveObject(omg8784, 5000, 2245.5, -4177.599609375, 81.099998474121, 0, 0, 0)
    setTimer(omgMoveomg8784, 5000, 1, 1)
  end
end

function omgMoveomg5008(point)
  if point == 1 then
    moveObject(omg5008, 20000, 2429.8999023438, -4599.7001953125, 19.60000038147, 0, 0, 0)
    setTimer(omgMoveomg5008, 20000, 1, 2)
  elseif point == 2 then
    moveObject(omg5008, 20000, 2330.1999511719, -4597.2998046875, 22.60000038147, 0, -9.75, 0)
    setTimer(omgMoveomg5008, 20000, 1, 3)
  elseif point == 3 then
    moveObject(omg5008, 20000, 2096.1999511719, -4591.7998046875, 110.09999847412, 0, -8.75, 0)
    setTimer(omgMoveomg5008, 20000, 1, 4)
  elseif point == 4 then
    moveObject(omg5008, 20000, 1931.9000244141, -4586.7001953125, 142.10000610352, 0, 0, 0)
    setTimer(omgMoveomg5008, 20000, 1, 5)
  elseif point == 5 then
    moveObject(omg5008, 20000, 1722.3000488281, -4611.7001953125, 142.10000610352, 0, 20.25, 102.75)
    setTimer(omgMoveomg5008, 20000, 1, 6)
  elseif point == 6 then
    moveObject(omg5008, 20000, 1745.9000244141, -4765.7001953125, 142.10000610352, 0, 0, 80)
    setTimer(omgMoveomg5008, 20000, 1, 7)
  elseif point == 7 then
    moveObject(omg5008, 20000, 1978.5999755859, -4746.6000976563, 142.10000610352, 0, 0, 0)
    setTimer(omgMoveomg5008, 20000, 1, 8)
  elseif point == 8 then
    moveObject(omg5008, 20000, 2560.3999023438, -4750.6000976563, 142.10000610352, 0, -4, -11.000030517578)
    setTimer(omgMoveomg5008, 20000, 1, 9)
  elseif point == 9 then
    moveObject(omg5008, 20000, 2602.8999023438, -4604.7998046875, 19.60000038147, 0, 2.25, -171.74996948242)
    setTimer(omgMoveomg5008, 20000, 1, 1)
  end
end

function omgMoveomg2310(point)
  if point == 1 then
    moveObject(omg2310, 5000, 2159.3999023438, -4728.7001953125, 8.8000001907349, 0, 0, 0)
    setTimer(omgMoveomg2310, 5000, 1, 2)
  elseif point == 2 then
    moveObject(omg2310, 5000, 2159.3999023438, -4728.7001953125, 8.8000001907349, 0, 0, 0)
    setTimer(omgMoveomg2310, 5000, 1, 3)
  elseif point == 3 then
    moveObject(omg2310, 5000, 2159.3999023438, -4728.7001953125, 8.8000001907349, 0, 0, 0)
    setTimer(omgMoveomg2310, 5000, 1, 1)
  end
end

function omgMoveomg9747(point)
  if point == 1 then
    moveObject(omg9747, 5000, 2641, -4372.8999023438, 8.8999996185303, 0, 0, 0)
    setTimer(omgMoveomg9747, 5000, 1, 2)
  elseif point == 2 then
    moveObject(omg9747, 5000, 2501.5, -4474.5, 8.8999996185303, 0, 0, 0)
    setTimer(omgMoveomg9747, 5000, 1, 3)
  elseif point == 3 then
    moveObject(omg9747, 5000, 2495, -4479.3999023438, 8.8999996185303, 0, 0, 0)
    setTimer(omgMoveomg9747, 5000, 1, 4)
  elseif point == 4 then
    moveObject(omg9747, 5000, 2495, -4479.3999023438, 8.8999996185303, 0, 0, 0)
    setTimer(omgMoveomg9747, 5000, 1, 5)
  elseif point == 5 then
    moveObject(omg9747, 5000, 2312.3000488281, -4612.2998046875, 8.8999996185303, 0, 0, 2.5)
    setTimer(omgMoveomg9747, 5000, 1, 6)
  elseif point == 6 then
    moveObject(omg9747, 5000, 2163.1999511719, -4721.1000976563, 8.8000001907349, 0, 0, 0)
    setTimer(omgMoveomg9747, 5000, 1, 7)
  elseif point == 7 then
    moveObject(omg9747, 5000, 2641, -4372.8999023438, 8.8999996185303, 0, 0, -2.5)
    setTimer(omgMoveomg9747, 5000, 1, 1)
  end
end

function omgMoveomg6088(point)
  if point == 1 then
    moveObject(omg6088, 5000, 2618, -4730.6000976563, 13.699999809265, 0, 0, -92.25)
    setTimer(omgMoveomg6088, 5000, 1, 2)
  elseif point == 2 then
    moveObject(omg6088, 5000, 2617.1000976563, -4618, 13.699999809265, 0, 0, 91.999984741211)
    setTimer(omgMoveomg6088, 5000, 1, 3)
  elseif point == 3 then
    moveObject(omg6088, 5000, 2571.3999023438, -4617.1000976563, 13.699999809265, 0, 0, 0)
    setTimer(omgMoveomg6088, 5000, 1, 4)
  elseif point == 4 then
    moveObject(omg6088, 5000, 2480.1999511719, -4612.2001953125, 13.5, 0, 0, 0)
    setTimer(omgMoveomg6088, 5000, 1, 5)
  elseif point == 5 then
    moveObject(omg6088, 5000, 2438.1000976563, -4611.6000976563, 21.5, 0, -8, 0)
    setTimer(omgMoveomg6088, 5000, 1, 6)
  elseif point == 6 then
    moveObject(omg6088, 5000, 2182.8000488281, -4613.2998046875, 27.5, 0, 1, 0)
    setTimer(omgMoveomg6088, 5000, 1, 7)
  elseif point == 7 then
    moveObject(omg6088, 5000, 1926.5999755859, -4606.7001953125, 27.5, 0, 7.25, -172.75001525879)
    setTimer(omgMoveomg6088, 5000, 1, 8)
  elseif point == 8 then
    moveObject(omg6088, 5000, 2030.4000244141, -4699, 30.200000762939, 0, 0, -44)
    setTimer(omgMoveomg6088, 5000, 1, 9)
  elseif point == 9 then
    moveObject(omg6088, 5000, 2125.6999511719, -4782.7998046875, 46.5, 0, -7, 0)
    setTimer(omgMoveomg6088, 5000, 1, 10)
  elseif point == 10 then
    moveObject(omg6088, 5000, 2375.6999511719, -4978.2001953125, 46.5, 0, 0, 0)
    setTimer(omgMoveomg6088, 5000, 1, 11)
  elseif point == 11 then
    moveObject(omg6088, 5000, 2646.1000976563, -4737, 13.699999809265, 0, 6.75, -142.99996948242)
    setTimer(omgMoveomg6088, 5000, 1, 1)
  end
end

function omgMoveomg5725(point)
  if point == 1 then
    moveObject(omg5725, 5000, 2228.3999023438, -4420.6000976563, 37, 0, 0, 0)
    setTimer(omgMoveomg5725, 5000, 1, 2)
  elseif point == 2 then
    moveObject(omg5725, 5000, 2227, -4549.6000976563, 37, 0, 0, 0)
    setTimer(omgMoveomg5725, 5000, 1, 3)
  elseif point == 3 then
    moveObject(omg5725, 5000, 2213.8999023438, -4709.8999023438, 37, 0, -22, 0)
    setTimer(omgMoveomg5725, 5000, 1, 4)
  elseif point == 4 then
    moveObject(omg5725, 5000, 2142.6000976563, -4851.3999023438, 37, 0, 0, 0)
    setTimer(omgMoveomg5725, 5000, 1, 5)
  elseif point == 5 then
    moveObject(omg5725, 5000, 2105.1000976563, -5051.2998046875, 37, 0, 0, 0)
    setTimer(omgMoveomg5725, 5000, 1, 6)
  elseif point == 6 then
    moveObject(omg5725, 5000, 2223.1000976563, -4859.3999023438, 37, 0, 0, 160)
    setTimer(omgMoveomg5725, 5000, 1, 7)
  elseif point == 7 then
    moveObject(omg5725, 5000, 2375.3000488281, -4796.2001953125, 37, 0, 0, 0)
    setTimer(omgMoveomg5725, 5000, 1, 8)
  elseif point == 8 then
    moveObject(omg5725, 5000, 2227.5, -4303.7001953125, 51.299999237061, 0, 22, -160)
    setTimer(omgMoveomg5725, 5000, 1, 1)
  end
end

addEventHandler("onResourceStart", getResourceRootElement(getThisResource()), omg_LOL)
