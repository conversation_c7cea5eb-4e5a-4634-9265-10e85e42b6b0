--[[
	VULTAIC MGM - BEÁLLÍTÁSOK RENDSZER SZERVER OLDAL

	Ez a fájl a beállítások rendszer szerver oldali logikáját tartalmazza.

	Főbb funkciók:
	- Játékos beállítások szinkronizálása
	- Element data kezelés
	- Kliens-szerver kommunikáció

	Működés:
	1. Kliens elküldi a beállításokat
	2. Szerver validálja az adatokat
	3. Element data-ként tárolja a beállításokat
	4. <PERSON>ás kliensek számára elérhetővé teszi

	Beállítás típusok:
	- Grafikai beállítások
	- Audio beállítások
	- Kontroll beállítások
	- Gameplay beállítások
]]

--[[
	JÁTÉKOS BEÁLLÍTÁSOK SZINKRONIZÁLÁSA

	Ez az event kezelő fogadja a kliens oldali beállításokat
	és element data-ként tárol<PERSON>ket a szerveren.
]]
addEvent("settings:syncPlayerSettings", true)
addEventHandler("settings:syncPlayerSettings", root,
function(data)
	-- Adatok validálása
	if type(data) == "table" then
		-- Minden beállítás element data-ként való tárolása
		for i, v in pairs(data) do
			-- setElementData(element, key, value, syncMode)
			-- syncMode = false: csak szerver oldali tárolás, nem szinkronizál klienseknek
			setElementData(source, i, v, false)
		end
	end
end)