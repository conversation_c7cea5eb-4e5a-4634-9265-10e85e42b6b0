--[[
    VULTAIC MGM - SYNC RENDSZER SZERVER OLDAL

    Ez a fájl a sync (szinkronizáció) rendszer szerver oldali logikáját tartalmazza.

    Főbb funkciók:
    - Arena specifikus adatok szinkronizálása
    - Element adatok kezelése arena szinten
    - Kliens-szerver adatszinkronizáció
    - Játékos arena belépés/kilépés kezelése
    - Adatok automatikus tisztítása

    Működés:
    1. Arena adatok tárolása arena -> element -> key -> value struktúrában
    2. Adatok szinkronizálása az arena összes játékosával
    3. Játékos kilépéskor adatok automatikus törlése
    4. Kliens oldali változtatások kezelése

    Event rendszer:
    - core:onPlayerJoinArena: Játékos arena belépés
    - core:onPlayerLeaveArena: Játékos arena kilépés
    - onClientChangeArenaData: Kliens oldali adatváltozás
    - onArenaDataChanged: Arena adat változás broadcast
]]

-- Arena adatok tárolása (arena -> element -> key -> value)
local data = {}

-- Event definíciók
addEvent("core:onPlayerJoinArena", true)     -- Játékos arénába lép
addEvent("core:onPlayerLeaveArena", true)    -- Játékos arénából kilép
addEvent("onClientChangeArenaData", true)    -- Kliens adatváltozás esemény

--[[
    Arena adat beállítása szerver oldalon
    @param element: az MTA element (pl. játékos, aréna)
    @param key: az adat kulcsa
    @param value: az adat értéke
    @param arenaElement: az aréna element (opcionális)
    @return boolean: sikeres volt-e a művelet
]]
--[[
    Beállít egy aréna specifikus adatot egy adott elemhez szerver oldalon, és szinkronizálja azt az aréna összes játékosával.
    @param element: az MTA element (pl. játékos, aréna)
    @param key: az adat kulcsa (string)
    @param value: az adat értéke (bármilyen típus)
    @param arenaElement: az aréna element (opcionális, ha nincs, automatikusan meghatározza)
    @return boolean: sikeres volt-e a művelet
    Lépések:
    1. Ellenőrzi, hogy az element létezik-e (biztonsági validáció).
    2. Ha nincs megadva aréna element, automatikusan meghatározza:
       - Ha maga az element egy aréna, azt használja.
       - Ha az element egy játékos, akkor a szülője (parent) az aréna.
       - Ha nem található aréna, hibával visszatér.
    3. Inicializálja a szerver oldali adatstruktúrát, ha még nem létezik az adott aréna/elemhez.
    4. Ha az új érték megegyezik a régivel (és nem tábla), nincs változás, visszatér.
    5. Beállítja az elementData-t (szerver oldali változás, szinkronizálódik a kliensekhez is).
    6. Kulcs/érték transzformáció (string→integer, optimalizáció miatt), majd eltárolja a szerver oldali táblában.
    7. Broadcast: minden aréna játékosnak elküldi, hogy megváltozott az adat (onArenaDataChanged event).
]]
function setArenaData(element, key, value, arenaElement)
    -- 1. Ellenőrizzük, hogy létezik-e az element (pl. játékos vagy aréna)
    if not isElement(element) then
        return false
    end

    -- 2. Ha nincs aréna element megadva, próbáljuk automatikusan meghatározni
    if not isElement(arenaElement) then
        if getElementType(element) == "arena" then
            -- Ha maga az element egy aréna, azt használjuk
            arenaElement = element
        elseif getElementType(element) == "player" and isElement(getElementParent(element)) then
            -- Ha az element egy játékos, a szülője az aréna
            arenaElement = getElementParent(element)
            if getElementType(arenaElement) ~= "arena" then
                return false
            end
        else
            -- Ha nem található aréna, hibával visszatér
            return false
        end
    end

    -- 3. Inicializáljuk a szerver oldali adatstruktúrát, ha szükséges
    if not data[arenaElement] then
        data[arenaElement] = {}
        data[arenaElement][element] = {}
    elseif not data[arenaElement][element] then
        data[arenaElement][element] = {}
    end

    -- 4. Ha az érték nem változott (és nem tábla), nincs teendő
    if type(value) ~= "table" and getElementData(element, key) == value then
        return false
    end

    -- 5. Element adat beállítása szerver oldalon (ez szinkronizálódik a kliensekhez is)
    setElementData(element, tostring(key), value, false)

    -- 6. Kulcs/érték transzformáció (string→integer, optimalizáció), majd eltárolás
    local key, value = transformData(key, value)
    data[arenaElement][element][key] = value

    -- 7. Broadcast: minden aréna játékosnak elküldjük a változást
    triggerClientEvent(arenaElement, "onArenaDataChanged", element, key, value)
end

-- Arena adat lekérdezése szerver oldalon
function getArenaData(element, key)
    return getElementData(element, key)
end

-- Teljes aréna adat elküldése egy játékosnak
--[[
    Egy játékosnak elküldi az összes aréna adatot, amikor belép az arénába.
    Ezt a függvényt általában a 'core:onPlayerJoinArena' event hívja meg.
    Nincs paramétere, a source automatikusan az aktuális játékos.
    Lépések:
    1. Megkeresi a játékos szülő elementjét (az arénát).
    2. Ellenőrzi, hogy az aréna element létezik-e és valóban aréna típusú-e.
    3. Ha igen, elküldi a kliensnek az aréna összes adatát (vagy üres táblát, ha nincs adat).
]]
function sendArenaData()
    -- Megkeressük a source (játékos) szülő elementjét, ami az aréna
    local arenaElement = getElementParent(source)
    -- Ellenőrizzük, hogy az aréna element létezik-e és tényleg "arena" típusú
    if not isElement(arenaElement) or getElementType(arenaElement) ~= "arena" then
        return false
    end
    -- Kliens oldali esemény: elküldjük a játékosnak az aréna összes adatát
    triggerClientEvent(source, "receiveArenaData", source, data[arenaElement] or {})
end
addEventHandler("core:onPlayerJoinArena", root, sendArenaData, true, "high")

--[[
    Egy játékos aréna elhagyásakor törli az összes hozzá tartozó aréna adatot.
    Általában a 'core:onPlayerLeaveArena' event hívja meg.
    Nincs paramétere, a source az aktuális játékos.
    Lépések:
    1. Megkeresi a játékos szülő elementjét (az arénát).
    2. Ellenőrzi, hogy az aréna element létezik-e és valóban aréna típusú-e.
    3. Ha van a játékoshoz tartozó aréna adat, minden kulcsot töröl az elementData-ból.
    4. Törli a játékos adatsorát a szerver oldali struktúrából is.
    5. Kliens oldalon is törli az aréna adatokat (receiveArenaData üres adattal).
    6. Kliens oldali broadcast: onArenaDataChanged (üresen) az aréna minden játékosának.
]]
function removePlayerArenaData()
    -- Megkeressük a source (játékos) szülő elementjét, ami az aréna
    local arenaElement = getElementParent(source)
    -- Ellenőrizzük, hogy az aréna element létezik-e és tényleg "arena" típusú
    if not isElement(arenaElement) or getElementType(arenaElement) ~= "arena" then
        return false
    end
    -- Ha van a játékoshoz tartozó aréna adat, minden kulcsot törlünk
    if data[arenaElement] and data[arenaElement][source] then
        for key, value in pairs(data[arenaElement][source]) do
            setElementData(source, tostring(key), nil, false)
        end
        data[arenaElement][source] = nil
    end
    -- Kliens oldali esemény: töröljük az aréna adatokat a játékosnál
    triggerClientEvent(source, "receiveArenaData", source, {})
    -- Broadcast minden aréna játékosnak, hogy az adat megváltozott (törlés)
    triggerClientEvent(arenaElement, "onArenaDataChanged", source)
end
addEventHandler("core:onPlayerLeaveArena", root, removePlayerArenaData, true, "low")

--[[
    Kliens oldali kérésre szerver oldalon beállít egy aréna adatot.
    Általában az onClientChangeArenaData event hívja meg.
    @param element: az MTA element (pl. játékos, aréna)
    @param key: az adat kulcsa (integer vagy string)
    @param value: az adat értéke (integer vagy string)
    Lépések:
    1. Megkeresi a source (játékos) szülő elementjét (az arénát).
    2. Ellenőrzi, hogy az aréna element létezik-e és valóban aréna típusú-e.
    3. A kulcsot és értéket visszaalakítja stringgé, ha integerként jött.
    4. Meghívja a setArenaData függvényt a megfelelő paraméterekkel.
]]
function clientSetArenaData(element, key, value)
    -- Megkeressük a source (játékos) szülő elementjét, ami az aréna
    local arenaElement = getElementParent(source)
    -- Ellenőrizzük, hogy az aréna element létezik-e és tényleg "arena" típusú
    if not isElement(arenaElement) or getElementType(arenaElement) ~= "arena" then
        return false
    end
    -- Kulcs/érték visszaalakítása stringgé, ha integerként jött
    local key, value = replaceDataIDs(key, value)
    -- Beállítjuk az aréna adatot szerver oldalon
    setArenaData(element, tostring(key), value, arenaElement)
end
addEventHandler("onClientChangeArenaData", root, clientSetArenaData)