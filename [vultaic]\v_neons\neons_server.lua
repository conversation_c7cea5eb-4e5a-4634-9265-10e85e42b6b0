--[[
	VULTAIC MGM - NEONS RENDSZER SZERVER OLDAL

	Ez a fájl a neons (neon világítás) rendszer szerver oldali logikáját tartalmazza.

	Főbb funkciók:
	- Neon világítás vásárlás és kezelés
	- DONATOR EXKLUZÍV funkció
	- Neon beállítások mentése/betöltése
	- Donator státusz ellenőrzés
	- Automatikus lejárat nem donatoroknak
	- MySQL integráció

	FONTOS: Neonok csak donatoroknak elérhetők!

	Működés:
	1. Donator stá<PERSON>z ellenőrzés vásárlásnál
	2. Neon beállítások mentése adatbázisba
	3. Automatikus lejárat ha megszűnik a donator státusz
	4. Szinkronizáció bejelentkezéskor
]]

--[[
	RESOURCE INDÍTÁS KEZELÉSE

	Neon árak betöltése és meglévő játékosok frissítése.
]]
addEventHandler("onResourceStart", resourceRoot,
function()
	-- Tuning árak lekérése
	local upgradePrices, exclusiveUpgrades = exports.v_tuning:getTuningUpgrades()
	neonsPrice = upgradePrices["Neons"]

	-- Meglévő játékosok neon adatainak frissítése
	for i, player in pairs(getElementsByType("player")) do
		updatePlayerNeons(player)
	end
end)

--[[
	Játékos neon adatainak frissítése

	@param element player - A játékos element

	Betölti és szinkronizálja a játékos neon beállításait.
	FONTOS: Donator státusz ellenőrzés - nem donatoroknak lejár!
]]
function updatePlayerNeons(player)
	if isElement(player) then
		-- Neon vásárlás ellenőrzése
		local bought = getPlayerTuningStats(player, "neons_bought")
		if bought then
			-- DONATOR ELLENŐRZÉS - kritikus!
			if not isPlayerDonator(player) then
				-- Nem donator -> neonok lejárnak
				return setElementData(player, "neons_bought", "expired")
			end

			-- Donator -> neonok aktívak
			setElementData(player, "neons_bought", true)

			-- Neon beállítások betöltése
			local data = getPlayerTuningStats(player, "neons")
			if data then
				setElementData(player, "neon", data)
			end
		end
	end
end

-- Event kezelők a bejelentkezéshez
addEvent("mysql:onPlayerLogin", true)
addEventHandler("mysql:onPlayerLogin", root, function() updatePlayerNeons(source) end)
addEventHandler("onPlayerLogin", root, function() updatePlayerNeons(source) end)

function handleNeonsPurchase()
	if not getElementData(source, "LoggedIn") then
		return triggerClientEvent(source, "notification:create", source, "Purchase failed", "You are not logged in")
	end
	local bought = getPlayerTuningStats(source, "neons_bought")
	if bought then
		return triggerClientEvent(source, "notification:create", source, "Purchase failed", "You have already bought neons")
	end
	local premium = isPlayerDonator(source)
	if not premium then
		return triggerClientEvent(source, "notification:create", source, "Purchase failed", "Become a donator to buy neons")
	end
	local money = tonumber(getElementData(source, "money") or 0)
	if money < neonsPrice then
		return triggerClientEvent(source, "notification:create", source, "Purchase failed", "You don't have enough money to buy neons")
	end
	setPlayerTuningStats(source, "neons_bought", "1")
	setElementData(source, "neons_bought", true)
	takePlayerStats(source, "money", neonsPrice)
	triggerClientEvent(source, "notification:create", source, "Purchase successful", "You have successfully bought neons")
end
addEvent("purchaseNeons", true)
addEventHandler("purchaseNeons", root, handleNeonsPurchase)

function handleNeonsUpdate(data)
	if type(data) ~= "table" then
		return print("Failed to update neons for "..getPlayerName(source))
	end
	local bought = getPlayerTuningStats(source, "neons_bought")
	if not bought then
		return triggerClientEvent(source, "notification:create", source, "Update failed", "You didn't buy neons")
	end
	setPlayerTuningStats(source, "neons", data)
	setElementData(source, "neon", data)
	print(getPlayerName(source).." has just updated his neons")
	triggerClientEvent(source, "notification:create", source, "Updated", "You have successfully updated your neons")
end
addEvent("updateNeons", true)
addEventHandler("updateNeons", root, handleNeonsUpdate)

setPlayerTuningStats = function(player, stats, value, ...)
	return exports.v_mysql:setPlayerTuningStats(player, stats, value, ...)
end

getPlayerTuningStats = function(player, stats, ...)
	return exports.v_mysql:getPlayerTuningStats(player, stats, ...)
end

takePlayerStats = function(player, stats, ...)
	return exports.v_mysql:takePlayerStats(player, stats, ...)
end

function isPlayerDonator(player)
	return exports.v_donatorship:isPlayerDonator(player)
end