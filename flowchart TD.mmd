flowchart TD
    A[🎮 Játékos csatlakozik arénába] --> B[🏟️ Arena pálya betöltést kezdeményez]
    B --> C[📋 mapmanager:loadMapData hívás]
    C --> D{📁 Cache fájl létezik?}
    
    D -->|Igen| E[📖 Cache fájl beolvasása]
    D -->|Nem| F[🗺️ Map XML feldolgozása]
    
    F --> G[🔧 Elements tábla generálása]
    G --> H[📝 JSON generálás: toJSON(elements)]
    H --> I[🔐 TEA titkosítás: teaEncode(json, key)]
    I --> J[💾 Cache fájl mentése: cache/HASH.edf]
    J --> K[🌐 HTTP URL generálás]
    
    E --> L[🔓 TEA dekódolás]
    L --> M[📋 JSON parsing: fromJSON()]
    
    K --> N[📤 Kliens felé küldés: sendMapData()]
    N --> O[📥 Kliens fogadja az adatokat]
    O --> P[🌐 HTTP letöltés: fetchRemote()]
    
    P --> Q{🌐 HTTP válasz OK?}
    Q -->|❌ 404 ERROR| R[💥 HIBA: Fájl nem található]
    Q -->|✅ 200 OK| S[📥 responseData fogadása]
    
    S --> T[🔓 TEA dekódolás: teaDecode()]
    T --> U[📋 JSON parsing: fromJSON()]
    U --> V{📋 JSON valid?}
    
    V -->|❌ Hibás| W[💥 HIBA: JSON parsing failed]
    V -->|✅ Valid| X[🗺️ loadMap(elements)]
    X --> Y[🎯 Pálya betöltve!]
    
    M --> X
    R --> Z[🔄 Retry vagy fallback]
    W --> Z
    
    style A fill:#e1f5fe
    style Y fill:#c8e6c9
    style R fill:#ffcdd2
    style W fill:#ffcdd2
    style Q fill:#fff3e0
    style V fill:#fff3e0