# Vultaic MGM - Konfigur<PERSON><PERSON>ó és Tesztelési Útmutató

## Tartalomjegyzék
1. [ACL.xml konfiguráció](#aclxml-konfiguráció)
2. [mtaserver.conf beállítása](#mtaserverconf-beállítása)
3. [Admin fiókok létrehozása](#admin-fiókok-létrehozása)
4. [Resource konfigurációk](#resource-konfigurációk)
5. [Szerver indítás és tesztelés](#szerver-indítás-és-tesztelés)
6. [Bejelentkezési rendszer tesztelése](#bejelentkezési-rendszer-tesztelése)
7. [Weboldal és API tesztelés](#weboldal-és-api-tesztelés)
8. [Teljesítmény optimalizálás](#teljesítmény-optimalizálás)
9. [Monitoring és logolás](#monitoring-és-logol<PERSON>)
10. [Hibaelh<PERSON><PERSON><PERSON><PERSON><PERSON>](#hibaelhár<PERSON>t<PERSON>)

---

## ACL.xml konfiguráció

### 1. ACL fájl szerkesztése
```bash
cd /opt/mta/mods/deathmatch/
sudo nano acl.xml
```

### 2. Komplett ACL konfiguráció
```xml
<?xml version="1.0" encoding="utf-8"?>
<acl>
    <!-- CSOPORTOK DEFINÍCIÓJA -->
    <group name="Console">
        <acl name="RPC" />
        <object name="user.Console" />
    </group>
    
    <group name="Admin">
        <acl name="Moderator" />
        <acl name="SuperModerator" />
        <acl name="Admin" />
        <object name="user.admin" />
        <!-- Admin serial-ok ide -->
        <object name="user.SERIAL1234567890ABCDEF1234567890AB" />
        <object name="user.SERIAL2234567890ABCDEF1234567890AB" />
    </group>
    
    <group name="SuperModerator">
        <acl name="Moderator" />
        <acl name="SuperModerator" />
        <!-- SuperMod serial-ok ide -->
        <object name="user.SERIAL3234567890ABCDEF1234567890AB" />
    </group>
    
    <group name="Moderator">
        <acl name="Moderator" />
        <!-- Moderátor serial-ok ide -->
        <object name="user.SERIAL4234567890ABCDEF1234567890AB" />
    </group>
    
    <group name="Everyone">
        <acl name="Default" />
        <object name="user.*" />
        <object name="resource.*" />
    </group>

    <!-- JOGOSULTSÁGOK DEFINÍCIÓJA -->
    <acl name="RPC">
        <right name="general.ModifyOtherObjects" access="true" />
        <right name="general.http" access="true" />
        <right name="command.start" access="true" />
        <right name="command.stop" access="true" />
        <right name="command.restart" access="true" />
        <right name="command.refresh" access="true" />
        <right name="command.shutdown" access="true" />
        <right name="function.*" access="true" />
        <right name="resource.*" access="true" />
        <right name="command.*" access="true" />
    </acl>
    
    <acl name="Admin">
        <right name="general.ModifyOtherObjects" access="true" />
        <right name="general.http" access="true" />
        
        <!-- Alapvető admin funkciók -->
        <right name="function.kickPlayer" access="true" />
        <right name="function.banPlayer" access="true" />
        <right name="function.setPlayerMoney" access="true" />
        <right name="function.givePlayerMoney" access="true" />
        <right name="function.takePlayerMoney" access="true" />
        <right name="function.setElementData" access="true" />
        <right name="function.getElementData" access="true" />
        
        <!-- Resource jogosultságok -->
        <right name="resource.v_mysql" access="true" />
        <right name="resource.v_login" access="true" />
        <right name="resource.v_awards" access="true" />
        <right name="resource.v_achievements" access="true" />
        <right name="resource.v_panel" access="true" />
        <right name="resource.v_notify" access="true" />
        <right name="resource.v_security" access="true" />
        <right name="resource.v_clans" access="true" />
        <right name="resource.v_settings" access="true" />
        <right name="resource.v_scoreboard" access="true" />
        <right name="resource.admin" access="true" />
        <right name="resource.admin2" access="true" />
        <right name="resource.v_admin" access="true" />
        <right name="resource.ipb" access="true" />
        
        <!-- Admin parancsok -->
        <right name="command.kick" access="true" />
        <right name="command.ban" access="true" />
        <right name="command.mute" access="true" />
        <right name="command.unmute" access="true" />
        <right name="command.freeze" access="true" />
        <right name="command.unfreeze" access="true" />
        <right name="command.slap" access="true" />
        <right name="command.slay" access="true" />
        <right name="command.setb" access="true" />
        <right name="command.get" access="true" />
        <right name="command.gete" access="true" />
        <right name="command.vlogin" access="true" />
        <right name="command.reloadawards" access="true" />
        <right name="command.award" access="true" />
        <right name="command.refreshmaps" access="true" />
    </acl>
    
    <acl name="SuperModerator">
        <right name="general.ModifyOtherObjects" access="true" />
        <right name="function.kickPlayer" access="true" />
        <right name="function.banPlayer" access="true" />
        <right name="function.setPlayerMoney" access="true" />
        <right name="resource.v_panel" access="true" />
        <right name="resource.v_notify" access="true" />
        <right name="resource.admin" access="true" />
        <right name="command.kick" access="true" />
        <right name="command.ban" access="true" />
        <right name="command.mute" access="true" />
        <right name="command.unmute" access="true" />
        <right name="command.freeze" access="true" />
        <right name="command.unfreeze" access="true" />
    </acl>
    
    <acl name="Moderator">
        <right name="function.kickPlayer" access="true" />
        <right name="resource.v_panel" access="true" />
        <right name="resource.v_notify" access="true" />
        <right name="command.kick" access="true" />
        <right name="command.mute" access="true" />
        <right name="command.unmute" access="true" />
    </acl>
    
    <acl name="Default">
        <right name="general.ModifyOtherObjects" access="false" />
        <right name="function.banPlayer" access="false" />
        <right name="function.kickPlayer" access="false" />
        <right name="command.start" access="false" />
        <right name="command.stop" access="false" />
        <right name="command.restart" access="false" />
    </acl>
</acl>
```

### 3. Admin serial-ok hozzáadása
```bash
# Serial lekérése (játékban /serial parancs)
# Vagy MTA kliens: Settings -> Advanced -> Show serial

# ACL szerkesztése
sudo nano acl.xml

# Serial hozzáadása az Admin csoporthoz:
<object name="user.YOUR_ACTUAL_SERIAL_HERE" />
```

---

## mtaserver.conf beállítása

### 1. Konfiguráció másolása és szerkesztése
```bash
cd /opt/mta/mods/deathmatch/
sudo cp resources/vultaic_mtaserver.conf mtaserver.conf
sudo nano mtaserver.conf
```

### 2. Fontos beállítások módosítása
```xml
<?xml version="1.0" encoding="utf-8"?>
<config>
    <!-- ALAPVETŐ SZERVER BEÁLLÍTÁSOK -->
    <servername>Your Vultaic MGM Server Name</servername>
    <owner_email_address><EMAIL></owner_email_address>
    <serverip>auto</serverip>
    <serverport>22003</serverport>
    <maxplayers>64</maxplayers>
    <httpport>22005</httpport>
    <httpdownloadurl>http://your-domain.com/mta/</httpdownloadurl>
    <password></password> <!-- Üres = nyilvános szerver -->
    
    <!-- KLIENS VERZIÓ KÖVETELMÉNYEK -->
    <minclientversion>1.6.0-9.23318.0</minclientversion>
    <minclientversion_auto_update>1</minclientversion_auto_update>
    
    <!-- HÁLÓZATI BEÁLLÍTÁSOK -->
    <ase>1</ase>
    <donotbroadcastlan>0</donotbroadcastlan>
    <fpslimit>100</fpslimit>
    <bandwidth_reduction>medium</bandwidth_reduction>
    
    <!-- SZINKRONIZÁCIÓS BEÁLLÍTÁSOK -->
    <player_sync_interval>100</player_sync_interval>
    <lightweight_sync_interval>1500</lightweight_sync_interval>
    <camera_sync_interval>500</camera_sync_interval>
    <ped_sync_interval>400</ped_sync_interval>
    <unoccupied_vehicle_sync_interval>400</unoccupied_vehicle_sync_interval>
    
    <!-- FÁJL ELÉRÉSI UTAK -->
    <idfile>server-id.keys</idfile>
    <logfile>logs/server.log</logfile>
    <authfile>logs/server_auth.log</authfile>
    <dbfile>logs/db.log</dbfile>
    <acl>acl.xml</acl>
    <scriptdebuglogfile>logs/scripts.log</scriptdebuglogfile>
    
    <!-- DEBUG BEÁLLÍTÁSOK -->
    <scriptdebugloglevel>1</scriptdebugloglevel>
    <htmldebuglevel>0</htmldebuglevel>
    <filter_duplicate_log_lines>1</filter_duplicate_log_lines>
    
    <!-- BIZTONSÁGI BEÁLLÍTÁSOK -->
    <auth_serial_groups>Admin</auth_serial_groups>
    <auth_serial_http>1</auth_serial_http>
    <auth_serial_http_ip_exceptions>127.0.0.1</auth_serial_http_ip_exceptions>
    <database_credentials_protection>1</database_credentials_protection>
    
    <!-- BACKUP BEÁLLÍTÁSOK -->
    <backup_path>backups</backup_path>
    <backup_interval>3</backup_interval>
    <backup_copies>5</backup_copies>
    
    <!-- MODULOK -->
    <module src="mta_mysql.so" />
    
    <!-- RESOURCE-OK -->
    <resource src="v_utils" startup="1" protected="0" />
    <resource src="core" startup="1" protected="0" />
    <resource src="v_mysql" startup="1" protected="0" />
    <resource src="v_login" startup="1" protected="0" />
    <resource src="v_security" startup="1" protected="0" />
    <resource src="v_achievements" startup="1" protected="0" />
    <resource src="v_panel" startup="1" protected="0" />
    <resource src="v_lobby" startup="1" protected="0" />
    <resource src="v_notify" startup="1" protected="0" />
    <resource src="v_settings" startup="1" protected="0" />
    <resource src="v_scoreboard" startup="1" protected="0" />
    <resource src="admin" startup="1" protected="0" />
    <resource src="admin2" startup="1" protected="0" />
    <resource src="v_admin" startup="1" protected="0" />
    <resource src="mapmanager" startup="1" protected="0" />
    <resource src="v_race" startup="1" protected="0" />
    <resource src="v_dm" startup="1" protected="0" />
    <resource src="v_shooter" startup="1" protected="0" />
</config>
```

---

## Admin fiókok létrehozása

### 1. Fórum admin fiók létrehozása
```bash
# phpBB admin létrehozása a telepítés során történt
# További adminok hozzáadása a fórum admin panelen keresztül
```

### 2. MTA szerver admin hozzáadása
```bash
# 1. Játékos serial lekérése
# 2. ACL.xml szerkesztése
sudo nano /opt/mta/mods/deathmatch/acl.xml

# 3. Serial hozzáadása az Admin csoporthoz
<object name="user.YOUR_SERIAL_HERE" />

# 4. ACL újratöltése (szerver újraindítás nélkül)
# Játékban admin parancs: /aclreload
```

### 3. Teszt admin fiók létrehozása adatbázisban
```sql
-- Kapcsolódás adatbázishoz
mysql -u mtauser -p v_accounts

-- Teszt admin fiók létrehozása
INSERT INTO vmtasa_accounts (
    username, 
    password_hash, 
    email, 
    serial, 
    money, 
    data, 
    tuning, 
    awards
) VALUES (
    'testadmin',
    '$2y$10$example_hash_here',
    '<EMAIL>',
    'YOUR_ADMIN_SERIAL_HERE',
    1000000,
    '{}',
    '{}',
    '[]'
);
```

---

## Resource konfigurációk

### 1. MySQL kapcsolat frissítése minden resource-ban
```bash
# Fő MySQL resource
sudo nano /opt/mta/mods/deathmatch/resources/[vultaic]/v_mysql/mysql_server.lua

# 59. sor módosítása:
g_Connection, error = dbConnect("mysql", "dbname="..DB_NAME..";host=127.0.0.1;port=3306", "mtauser", "MTA_Strong_Password_2024!")

# Achievements resource
sudo nano /opt/mta/mods/deathmatch/resources/[vultaic]/v_achievements/achievements_s.lua

# 77. sor módosítása:
g_Connection, error = dbConnect("mysql", "dbname="..DB_NAME..";host=127.0.0.1;port=3306", "mtauser", "MTA_Strong_Password_2024!")

# Clans resource
sudo nano /opt/mta/mods/deathmatch/resources/[vultaic]/v_clans/clan_server.lua

# 58. sor módosítása:
g_Connection, error = dbConnect("mysql", "dbname="..DB_NAME..";host=127.0.0.1;port=3306", "mtauser", "MTA_Strong_Password_2024!")

# Toptimes resource
sudo nano /opt/mta/mods/deathmatch/resources/[vultaic]/v_toptimes/toptimes_server.lua

# 48. sor módosítása:
toptimes.connection = dbConnect("mysql", "dbname=mta_toptimes;host=127.0.0.1;port=3306", "mtauser", "MTA_Strong_Password_2024!")
```

### 2. Login API URL-ek ellenőrzése
```bash
sudo nano /opt/mta/mods/deathmatch/resources/[vultaic]/v_login/login_server.lua

# 29. sor ellenőrzése:
login.apiURL = getServerPort() == 22003 and "https://your-domain.com/mta_login_test.php" or "https://your-domain.com/mta_login_dev.php"
```

### 3. Avatar URL frissítése
```bash
sudo nano /opt/mta/mods/deathmatch/resources/[vultaic]/v_login/avatars_server.lua

# 1. és 18. sor módosítása:
local pageURL = "https://your-domain.com/uploads/"
fetchRemote("https://your-domain.com/mta_getavatar.php", 1, 10000, receiveAvatarContent, POST, false, username, source)
```

---

## Szerver indítás és tesztelés

### 1. Szerver indítása
```bash
cd /opt/mta
sudo -u mtaserver screen -S mta-server ./mta-server64

# Vagy systemd service-szel:
sudo systemctl start mta-server
```

### 2. Logok ellenőrzése
```bash
# Fő szerver log
tail -f /opt/mta/mods/deathmatch/logs/server.log

# Script logok
tail -f /opt/mta/mods/deathmatch/logs/scripts.log

# MySQL kapcsolatok ellenőrzése
grep -i "mysql.*connected" /opt/mta/mods/deathmatch/logs/scripts.log
```

### 3. Resource állapotok ellenőrzése
```bash
# MTA szerver konzolban:
list

# Keresendő resource-ok:
# v_mysql [running]
# v_login [running]  
# v_achievements [running]
# core [running]
# admin [running]
```

### 4. Port elérhetőség tesztelése
```bash
# MTA szerver port
telnet your-server-ip 22003

# HTTP port
curl http://your-server-ip:22005

# Webszerver
curl http://your-domain.com
```

---

## Bejelentkezési rendszer tesztelése

### 1. Fórum regisztráció tesztelése
```bash
# Böngészőben nyisd meg:
http://your-domain.com/forum

# Regisztrálj egy teszt felhasználót:
# Username: testuser
# Password: testpass123
# Email: <EMAIL>
```

### 2. MTA bejelentkezés tesztelése
```bash
# Csatlakozz a szerverre MTA kliensből
# IP: your-server-ip:22003

# Bejelentkezési ablakban:
# Username: testuser
# Password: testpass123
```

### 3. API végpontok tesztelése
```bash
# Login API teszt
curl -X POST http://your-domain.com/mta_login_test.php \
  -H "Content-Type: application/json" \
  -d '{"username":"testuser","password":"testpass123"}'

# Várt válasz:
# {"error":0,"connect_id":1,"username":"testuser","donator":false}

# Avatar API teszt
curl -X POST http://your-domain.com/mta_getavatar.php \
  -H "Content-Type: application/json" \
  -d '{"username":"testuser"}'
```

### 4. Adatbázis integráció ellenőrzése
```sql
-- Bejelentkezés után ellenőrizd:
mysql -u mtauser -p v_accounts

SELECT * FROM vmtasa_accounts WHERE username = 'testuser';

-- Ellenőrizd, hogy létrejött-e a rekord
```

---

## Weboldal és API tesztelés

### 1. Weboldal elérhetőség
```bash
# Főoldal
curl -I http://your-domain.com

# Fórum
curl -I http://your-domain.com/forum

# Statisztikák
curl -I http://your-domain.com/stats
```

### 2. SSL tanúsítvány ellenőrzése
```bash
# SSL teszt
curl -I https://your-domain.com

# Tanúsítvány részletek
openssl s_client -connect your-domain.com:443 -servername your-domain.com
```

### 3. PHP hibák ellenőrzése
```bash
# PHP error log
sudo tail -f /var/log/php8.1-fpm.log

# Nginx error log
sudo tail -f /var/log/nginx/error.log
```

---

## Teljesítmény optimalizálás

### 1. MySQL optimalizálás
```bash
sudo nano /etc/mysql/mariadb.conf.d/50-server.cnf
```

```ini
[mysqld]
# Alapvető beállítások
max_connections = 200
innodb_buffer_pool_size = 512M
innodb_log_file_size = 128M
query_cache_size = 64M
query_cache_limit = 2M

# Teljesítmény beállítások
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT
tmp_table_size = 64M
max_heap_table_size = 64M
```

### 2. PHP-FPM optimalizálás
```bash
sudo nano /etc/php/8.1/fpm/pool.d/www.conf
```

```ini
pm = dynamic
pm.max_children = 50
pm.start_servers = 10
pm.min_spare_servers = 5
pm.max_spare_servers = 20
pm.max_requests = 500
```

### 3. Nginx optimalizálás
```bash
sudo nano /etc/nginx/nginx.conf
```

```nginx
worker_processes auto;
worker_connections 1024;

# Gzip tömörítés
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/plain text/css application/json application/javascript text/xml application/xml;

# Fájl cache
open_file_cache max=1000 inactive=20s;
open_file_cache_valid 30s;
open_file_cache_min_uses 2;
```

---

## Monitoring és logolás

### 1. Szerver monitoring script
```bash
sudo nano /opt/scripts/server_monitor.sh
```

```bash
#!/bin/bash

LOG_FILE="/var/log/mta_monitor.log"
DATE=$(date '+%Y-%m-%d %H:%M:%S')

# MTA szerver ellenőrzése
if ! pgrep -f "mta-server64" > /dev/null; then
    echo "[$DATE] MTA Server is down! Restarting..." >> $LOG_FILE
    systemctl restart mta-server
fi

# MySQL ellenőrzése
if ! systemctl is-active --quiet mariadb; then
    echo "[$DATE] MySQL is down! Restarting..." >> $LOG_FILE
    systemctl restart mariadb
fi

# Nginx ellenőrzése
if ! systemctl is-active --quiet nginx; then
    echo "[$DATE] Nginx is down! Restarting..." >> $LOG_FILE
    systemctl restart nginx
fi

# Disk space ellenőrzése
DISK_USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 90 ]; then
    echo "[$DATE] Disk usage is ${DISK_USAGE}%! Clean up required." >> $LOG_FILE
fi
```

```bash
# Jogosultságok és crontab
sudo chmod +x /opt/scripts/server_monitor.sh
sudo crontab -e

# 5 percenként ellenőrzés
*/5 * * * * /opt/scripts/server_monitor.sh
```

### 2. Log rotáció beállítása
```bash
sudo nano /etc/logrotate.d/mta-server
```

```
/opt/mta/mods/deathmatch/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    copytruncate
}
```

---

## Hibaelhárítás

### Gyakori problémák és megoldások

#### 1. Szerver nem indul el
```bash
# Jogosultságok ellenőrzése
ls -la /opt/mta/mta-server64

# Port foglaltság
sudo netstat -tulpn | grep 22003

# Konfiguráció szintaxis
xmllint /opt/mta/mods/deathmatch/mtaserver.conf
xmllint /opt/mta/mods/deathmatch/acl.xml
```

#### 2. MySQL kapcsolat hiba
```bash
# Szolgáltatás állapota
sudo systemctl status mariadb

# Kapcsolat teszt
mysql -u mtauser -p v_accounts

# Jogosultságok ellenőrzése
mysql -u root -p
SHOW GRANTS FOR 'mtauser'@'localhost';
```

#### 3. Bejelentkezés nem működik
```bash
# API végpont teszt
curl -X POST http://your-domain.com/mta_login_test.php \
  -H "Content-Type: application/json" \
  -d '{"username":"test","password":"test"}'

# PHP hibák
sudo tail -f /var/log/php8.1-fpm.log

# MTA login log
grep -i login /opt/mta/mods/deathmatch/logs/scripts.log
```

#### 4. Resource hibák
```bash
# Resource újraindítása
# MTA konzolban: restart resourcename

# Meta.xml ellenőrzése
xmllint /opt/mta/mods/deathmatch/resources/[vultaic]/v_mysql/meta.xml

# Jogosultságok
sudo chown -R mtaserver:mtaserver /opt/mta/mods/deathmatch/resources/
```

---

## Végső ellenőrzési lista

### ✅ Szerver működés
- [ ] MTA szerver elindul hibák nélkül
- [ ] Minden resource betöltődik
- [ ] MySQL kapcsolatok működnek
- [ ] Portok elérhetők

### ✅ Bejelentkezési rendszer
- [ ] Fórum regisztráció működik
- [ ] MTA bejelentkezés működik
- [ ] API végpontok válaszolnak
- [ ] Adatbázis integráció működik

### ✅ Weboldal
- [ ] Főoldal elérhető
- [ ] Fórum működik
- [ ] SSL tanúsítvány érvényes
- [ ] Statisztika oldal működik

### ✅ Biztonság
- [ ] Tűzfal konfigurálva
- [ ] SSL beállítva
- [ ] Adatbázis jelszavak frissítve
- [ ] Admin jogosultságok beállítva

### ✅ Monitoring
- [ ] Logok működnek
- [ ] Backup script beállítva
- [ ] Monitoring script aktív
- [ ] Log rotáció konfigurálva

---

*Ez az útmutató a Vultaic MGM szerver teljes konfigurációját és tesztelését mutatja be. A rendszer most készen áll a használatra!*
