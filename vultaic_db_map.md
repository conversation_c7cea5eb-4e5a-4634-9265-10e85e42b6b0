# Vultaic-MGM-master – <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> és Lekérdezések Összefoglaló

Ez a dokumentum a Vultaic-MGM-master s<PERSON><PERSON><PERSON><PERSON> kódb<PERSON>zisának adatbázis-kez<PERSON><PERSON><PERSON><PERSON>, kap<PERSON><PERSON><PERSON><PERSON><PERSON> főbb függvényeit, sémáit és biztonsági szempontjait foglalja össze. Célja, hogy átláthatóvá tegye az összes adatbázis-interakciót, a felhasznált táblákat, valamint a lekérdezési és módosítási logikákat.

---

## 1. Adatbázis-kapcsolat és alapbeállítások

- **Kapcsolat létrehozása:**
  - `dbConnect("mysql", "dbname=...;host=127.0.0.1;port=3306", "root", "[jelszó]")` (lásd: `[vultaic]/v_mysql/mysql_server.lua`)
  - Fejlesztői és éles adatbázis: `v_accounts_dev` és `v_accounts`.
  - A kapcsolatot a `g_Connection` változó tárolja.

- **Főbb kapcsolódó resource-ok:**
  - `v_login` (bejelentkezés)
  - `v_awards` (achievement rendszer)

## 2. Főbb adatbázis-táblák

- **vmtasa_accounts**: felhasználói fiókadatok, statisztikák, tuning, awards (JSON-ban tárolva)
  - Főbb mezők: `account_id`, `money`, `dm_points`, `os_points`, `dd_points`, `race_points`, `shooter_points`, `hunter_points`, `tdm_points`, `Clan`, `data` (JSON), `tuning` (JSON)

## 3. Főbb adatbázis-függvények és exportok

- `[vultaic]/v_mysql/mysql_server.lua`:
  - **Kapcsolatkezelés:**
    - `onScriptLoad()`, `onScriptUnload()`
  - **Exportált függvények:**
    - `setPlayerStats(player, kulcs, érték, isTemporary)` – statisztika írása
    - `getPlayerStats(player, kulcs)` – statisztika lekérdezése
    - `takePlayerStats(player, kulcs, mennyiség)` – stat csökkentése
    - `givePlayerStats(player, kulcs, mennyiség, forceSet)` – stat növelése
    - `setPlayerTuningStats(player, kulcs, érték)` – tuning beállítása
    - `getPlayerTuningStats(player, kulcs)` – tuning lekérdezése
    - `givePlayerAward(player, awardName)` – achievement kiosztás

- `[vultaic]/v_mysql/CAccount.lua`:
  - **Felhasználói fiók objektumkezelés:**
    - `CAccount:new(player, userdata)` – account betöltése/új létrehozása
    - `CAccount.constructor(query, player, userdata)` – lekérdezés eredményének feldolgozása
    - `CAccount.destructor(player)` – fiók mentése, kilépés
    - `CAccount:setPlayerStats`, `CAccount:getPlayerStats`, stb. – statisztika kezelés
    - Minden adatbázis művelet a `vmtasa_accounts` táblán történik, JSON mezők használatával (data, tuning, awards)

- `[vultaic]/v_mysql/server_commands.lua`:
  - Parancs alapú stat lekérdezés/állítás: `setb`, `get`, `gete` (admin jog szükséges)

## 4. Lekérdezési és mentési logika

- **Lekérdezés:**
  - `dbQuery(CAccount.constructor, {player, userdata}, g_Connection, "SELECT * FROM vmtasa_accounts WHERE account_id = ? LIMIT 1", userdata.connect_id)`

- **Mentés:**
  - `dbExec(g_Connection, 'UPDATE vmtasa_accounts SET ... WHERE account_id = ...', ...)` (lásd: CAccount.destructor)
  - Az összetett mezők (data, tuning, awards) JSON-ként kerülnek mentésre.

## 5. Biztonsági szempontok

- **Input validáció:**
  - A lekérdezések paraméterezettek, így SQL injection elleni alapvédelem biztosított.
  - A statisztika kulcsok és értékek validációja javasolt a CAccount függvényekben (típusellenőrzés, engedélyezett kulcsok listája).

- **Jogosultságok:**
  - Admin parancsok csak megfelelő ACL/jogosultság mellett elérhetők.
  - A statisztika módosító parancsok (setb, get, gete) csak admin számára elérhetők.

- **Jelszókezelés:**
  - A MySQL jelszó a forráskódban van, javasolt környezeti változóba vagy külön configba helyezni.

---

## 6. Összegzés, javaslatok

- Az adatbázis-kezelés központilag, CAccount osztályon keresztül történik.
- A legtöbb adat JSON mezőkben tárolódik, ez rugalmas, de validációt igényel.
- Javasolt a kulcsok és értékek szigorúbb ellenőrzése, valamint a jelszó biztonságosabb tárolása.
- Az exportált függvények átláthatóak, a statisztika-kezelés jól elkülönül.

---

*Ez a dokumentum a Vultaic-MGM-master szerveroldali adatbázis-interakcióinak áttekintését szolgálja, audit és fejlesztési célra. További részletekért lásd a forrásfájlokat: `[vultaic]/v_mysql/mysql_server.lua`, `CAccount.lua`, `server_commands.lua`.*
