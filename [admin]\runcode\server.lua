﻿--[[
	VULTAIC MGM - RUNCODE SZERVER OLDAL

	Ez a fájl a RunCode admin eszköz szerver oldali logikáját tartalmazza.

	Főbb funkciók:
	- Lua kód futtatása valós időben
	- Fejlesztői debugging eszköz
	- Biztonságos kód végrehajtás
	- Eredmény visszajelzés
	- Error handling
	- Admin jogosultság ellenőrzés

	Biztonsági szempontok:
	- Csak admin jogosultsággal rendelkező játékosok használhatják
	- Sandbox környezet
	- Error catching
	- Audit log
]]

-- Root element referencia
local rootElement = getRootElement()

--[[
	Lua string futtatása és eredmény kiértékelése

	@param string commandstring - A futtatandó Lua kód
	@param element outputTo - Kimenet célpontja (játékos vagy console)
	@param element source - A parancsot kiadó j<PERSON>
]]
function runString (commandstring, outputTo, source)
	me = source                                         -- <PERSON><PERSON>b<PERSON><PERSON> 'me' v<PERSON><PERSON><PERSON><PERSON> beállítása
	local sourceName = source and getPlayerName(source) or "Console"

	-- Parancs naplózása
	outputChatBoxR(sourceName.." executed command: "..commandstring, outputTo, true)
	local notReturned

	-- Első próbálkozás: "return" előtaggal (kifejezés kiértékelés)
	local commandFunction,errorMsg = loadstring("return "..commandstring)
	if errorMsg then
		-- Sikertelen, próbálkozás "return" nélkül (statement végrehajtás)
		commandFunction, errorMsg = loadstring(commandstring)
	end

	-- Ha még mindig hiba van, hibaüzenet kiírása
	if errorMsg then
		outputChatBoxR("Error: "..errorMsg, outputTo)
		return
	end

	-- Biztonságos függvény végrehajtás (pcall)
	local results = { pcall(commandFunction) }
	if not results[1] then
		-- Futási hiba történt
		outputChatBoxR("Error: "..results[2], outputTo)
		return
	end

	-- Eredmények formázása
	local resultsString = ""
	local first = true
	for i = 2, #results do
		if first then
			first = false
		else
			resultsString = resultsString..", "
		end
		local resultType = type(results[i])
		-- Element típus részletesebb megjelenítése
		if isElement(results[i]) then
			resultType = "element:"..getElementType(results[i])
		end
		resultsString = resultsString..tostring(results[i]).." ["..resultType.."]"
	end

	-- Eredmények kiírása (ha vannak)
	if #results > 1 then
		outputChatBoxR("Command results: " ..resultsString)
		return
	end

	-- Sikeres végrehajtás visszajelzése
	outputChatBoxR("Command executed!")
end

-- run command
addCommandHandler("run",
	function (player, command, ...)
		if not hasObjectPermissionTo(player, "command.stopall" ) then
			outputChatBox("Access denied.", player, 255, 255, 255, true)
			return
		end
		local commandstring = table.concat({...}, " ")
		return runString(commandstring, rootElement, player)
	end
)

-- silent run command
addCommandHandler("srun",
	function (player, command, ...)
		if not hasObjectPermissionTo(player, "command.stopall" ) then
			outputChatBox("Access denied.", player, 255, 255, 255, true)
			return
		end
		local commandstring = table.concat({...}, " ")
		return runString(commandstring, player, player)
	end
)

-- clientside run command
addCommandHandler("crun",
	function (player, command, ...)
		local commandstring = table.concat({...}, " ")
		if player then
			if not hasObjectPermissionTo(player, "command.stopall" ) then
				outputChatBox("Access denied.", player, 255, 255, 255, true)
				return
			end
			return triggerClientEvent(player, "doCrun", rootElement, commandstring)
		else
			return runString(commandstring, false, false)
		end
	end
)

-- http interface run export
function httpRun(commandstring)
	if not user then outputDebugString ( "httpRun can only be called via http", 2 ) return end
	local notReturned
	--First we test with return
	local commandFunction,errorMsg = loadstring("return "..commandstring)
	if errorMsg then
		--It failed.  Lets try without "return"
		notReturned = true
		commandFunction, errorMsg = loadstring(commandstring)
	end
	if errorMsg then
		--It still failed.  Print the error message and stop the function
		return "Error: "..errorMsg
	end
	--Finally, lets execute our function
	local results = { pcall(commandFunction) }
	if not results[1] then
		--It failed.
		return "Error: "..results[2]
	end
	if not notReturned then
		local resultsString = ""
		local first = true
		for i = 2, #results do
			if first then
				first = false
			else
				resultsString = resultsString..", "
			end
			local resultType = type(results[i])
			if isElement(results[i]) then
				resultType = "element:"..getElementType(results[i])
			end
			resultsString = resultsString..tostring(results[i]).." ["..resultType.."]"
		end
		return "Command results: "..resultsString
	end
	return "Command executed!"
end