--[[
    Vultaic MGM - sync_shared.lua
    <PERSON>tes magyar kommentek minden sorhoz
--]]

--[[
    Az integer kulcsok gyorsabbak, mint a stringek Lua-ban.
    Ezért a szinkronizáció során a gyakran használt string kulcsokat integerre konvertáljuk,
    majd v<PERSON> stringgé, amikor szükséges.
]]

-- Azon kulcsok listája, amelyeket integerként is kezelünk
local replace = {
    "arena",        -- 1
    "state",        -- 2
    "spectating",   -- 3
    "waiting",      -- 4
    "alive",        -- 5
    "dead",         -- 6
    "spectating",   -- 7 (dup<PERSON><PERSON><PERSON>, de a logika miatt lehet)
    "training"      -- 8
}

-- Egy dictionary, ami a string kulcsokat integerre fordítja
local replace_dict = {}
-- A replace listában lévő kulcsokat integerre konvertáljuk
for id, value in pairs(replace) do
    -- A kulcsot integerként tároljuk a dictionary-ben
    replace_dict[value] = id
end

--[[
    Kulcs és érték transzformálása integerre, ha benne van a replace_dict-ben
    @param key: string vagy integer kulcs
    @param value: string vagy integer érték
    @return: integer vagy eredeti érték
]]
-- A kulcs és érték transzformálásáért felelős függvény
function transformData(key, value)
    -- A kulcsot integerre konvertáljuk, ha benne van a replace_dict-ben
    local key = replace_dict[key] or key
    -- Az értéket integerre konvertáljuk, ha benne van a replace_dict-ben
    local value = replace_dict[value] or value
    -- A transzformált kulcsot és értéket visszaadjuk
    return key, value
end

--[[
    Integer kulcs/érték visszaalakítása stringgé, ha benne van a replace listában
    @param key: integer vagy string kulcs
    @param value: integer vagy string érték
    @return: string vagy eredeti érték
]]
-- A kulcs és érték visszaalakításáért felelős függvény
function replaceDataIDs(key, value)
    -- A kulcsot stringre konvertáljuk, ha benne van a replace listában
    local key = replace[key] or key
    -- Az értéket stringre konvertáljuk, ha benne van a replace listában
    local value = replace[value] or value
    -- A visszaalakított kulcsot és értéket visszaadjuk
    return key, value
end