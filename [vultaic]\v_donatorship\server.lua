--[[
	VULTAIC MGM - DONATORSHIP RENDSZER SZERVER OLDAL

	Ez a fájl a donatorship (támogatói) rendszer szerver oldali logikáját tartalmazza.

	Főbb funkciók:
	- Donator stá<PERSON>z kezelése
	- Donator jutalmak kiosztása
	- <PERSON><PERSON> jogosultságok donator státuszként kezelése
	- Bejelentkezési jutalmak
	- Donator státusz ellenőrzés
	- Logging rendszer integráció

	Donator státusz megszerzése:
	1. Aktív donáció a fórumon
	2. Admin szint 3 vagy magasabb (automatikus donator)

	Jutalom rendszer:
	- Aktív donáció * 25,000$ jutalom bejelentkezéskor
	- Donator státusz üzenet megjelenítése
	- Tranzakciók naplózása
]]

--[[
	JÁTÉKOS CSATLAKOZÁS KEZELÉSE

	Alapértelmezetten minden játékos nem donator stá<PERSON>szal indul.
]]
addEventHandler("onPlayerJoin", root,
function()
	setElementData(source, "donator", false)
end)

--[[
	JÁTÉKOS BEJELENTKEZÉS KEZELÉSE (MTA ACCOUNT)

	MTA account bejelentkezéskor ellenőrzi a donator státuszt.
]]
addEventHandler("onPlayerLogin", root,
function()
	checkDonatorShip(source)
end)

--[[
	Donator státusz ellenőrzése

	@param element player - A játékos element

	Ellenőrzi és beállítja a donator státuszt ha szükséges.
]]
function checkDonatorShip(player)
	if isElement(player) then
		if not getElementData(player, "donator") then
			local donator = true--isPlayerDonator(player)  -- Jelenleg minden játékos donator (teszt)
			if donator then
				setElementData(player, "donator", true)
			end
		end
	end
end

--[[
	Fórum bejelentkezés kezelése

	@param table data - A bejelentkezési adatok

	Fórum alapú bejelentkezéskor donator státusz beállítása.
]]
function onPlayerLogin(data)
	local donator = true--data.donator and true or false  -- Jelenleg minden játékos donator (teszt)
	print(getPlayerName(source).." has logged in, DONATOR: "..tostring(donator))
	if donator then
		setElementData(source, "donator", true)
		outputChatBox("[Donator] #FFFFFFYour donator status is #00FF00active", source, 25, 132, 109, true)
	end
end
addEvent("login:onPlayerLogin", true)
addEventHandler("login:onPlayerLogin", root, onPlayerLogin)

function checkPlayerReward()
	--print("Checking reward for "..getPlayerName(source))
	local userdata = getElementData(source, "userdata") or {}
	local donated = tonumber(userdata.active_donation) or 0
	--print("Active donations for "..getPlayerName(source)..": "..donated)	
	if donated > 0 then
		local reward = donated * 25000
		exports.v_mysql:givePlayerStats(source, "money", reward)
		outputChatBox("[Donator] #FFFFFFYou have been rewarded with #19846D$"..reward.."#FFFFFF, thanks for your support!", source, 25, 132, 109, true)
		DLog.player(source, string.gsub(getPlayerName(source), "#%x%x%x%x%x%x", "").." has been awarded $"..reward.." for donating.")
	end
end
addEvent("mysql:onPlayerLogin", true)
addEventHandler("mysql:onPlayerLogin", root, checkPlayerReward)

function isPlayerDonator(player)
	if isElement(player) then
		if getElementData(player, "donator") then
			return true
		else
			local admin_level = getElementData(player, "admin_level") or 1
			if admin_level > 2 then
				return true
			else
				return false
			end
		end
		return false
	end
	return false
end

addCommandHandler("donator",
function(player, command, ...)
	local donator = isPlayerDonator(player)
	if donator then
		outputChatBox("You are a donator", player, 0, 255, 0)
	else
		outputChatBox("You are not a donator", player, 255, 0, 0)
	end
end)