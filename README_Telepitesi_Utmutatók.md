# Vultaic MGM - Komplett Telepítési Útmutatók

## 📋 Áttekintés

Ez a dokumentumcsomag a **Vultaic MGM (Multi Gamemode Manager)** teljes telepítését mutatja be Linux VPS szerveren, weboldallal, fórummal és adatbázissal együtt.

### 🎯 Mit tartalmaz a rendszer?
- **MTA:SA szerver** - Multi Theft Auto: San Andreas szerver
- **MySQL/MariaDB adatbázis** - Játékos adatok, statisztikák tárolása
- **Weboldal és fórum** - phpBB vagy IPB alapú közösségi platform
- **API integráció** - Fórum-alapú bejelentkezési rendszer
- **Admin rendszer** - Komplett adminisztrációs felület

---

## 📚 Útmutatók sorrendje

### 1️⃣ [VPS Szerver Telepítés](01_VPS_Szerver_Telepites.md)
**Időtartam:** ~2-3 óra

**Mit tartalmaz:**
- Linux VPS előkészítése (Ubuntu 20.04/22.04)
- Szükséges csomagok telepítése
- MTA szerver letöltése és beállítása
- MySQL/MariaDB telepítése
- Alapvető biztonsági beállítások
- Tűzfal konfiguráció

**Előfeltételek:**
- VPS hozzáférés (SSH)
- Minimum 2GB RAM, 2 CPU core
- Ubuntu 20.04+ operációs rendszer

---

### 2️⃣ [Adatbázis Beállítás](02_Adatbazis_Beallitas.md)
**Időtartam:** ~1-2 óra

**Mit tartalmaz:**
- Adatbázis struktúra létrehozása
- Felhasználói táblák konfigurálása
- Achievement és klán rendszer táblák
- MySQL biztonsági beállítások
- Backup rendszer beállítása
- Resource konfigurációk frissítése

**Fontos:** 
- ⚠️ **BIZTONSÁGI FIGYELMEZTETÉS** - A hardkódolt jelszavakat kötelező megváltoztatni!

---

### 3️⃣ [Weboldal és Fórum](03_Weboldal_es_Forum.md)
**Időtartam:** ~3-4 óra

**Mit tartalmaz:**
- Nginx webszerver beállítása
- PHP 8.1+ konfiguráció
- SSL tanúsítvány (Let's Encrypt)
- phpBB vagy IPB fórum telepítése
- MTA API végpontok létrehozása
- Statisztika weboldal készítése

**Választható fórum rendszerek:**
- **phpBB** - Ingyenes, nyílt forráskódú
- **IPB** - Fizetős, professzionális

---

### 4️⃣ [Konfiguráció és Tesztelés](04_Konfiguracio_es_Teszteles.md)
**Időtartam:** ~2-3 óra

**Mit tartalmaz:**
- ACL.xml admin jogosultságok
- mtaserver.conf teljes konfigurációja
- Admin fiókok létrehozása
- Teljes rendszer tesztelése
- Teljesítmény optimalizálás
- Monitoring és logolás beállítása

---

## 🚀 Gyors telepítési áttekintés

### Minimális rendszerkövetelmények
```
CPU: 2 cores
RAM: 2GB (4GB ajánlott)
Storage: 20GB SSD
OS: Ubuntu 20.04/22.04 LTS
Network: 100Mbps
```

### Szükséges portok
```
22003/tcp+udp - MTA szerver
22005/tcp     - MTA HTTP
80/tcp        - Webszerver (HTTP)
443/tcp       - Webszerver (HTTPS)
3306/tcp      - MySQL (csak localhost)
22/tcp        - SSH
```

### Telepítési idő összesen
- **Tapasztalt rendszergazda:** 4-6 óra
- **Kezdő:** 8-12 óra (dokumentáció olvasással)

---

## ⚠️ Kritikus biztonsági figyelmeztetések

### 🔐 Jelszavak megváltoztatása KÖTELEZŐ!

A rendszerben jelenleg hardkódolt jelszavak vannak:

```lua
-- VESZÉLYES - AZONNAL VÁLTOZTASD MEG!
password: "M1RAg3_Zz@ST3R"
user: "root"
```

**Érintett fájlok:**
- `[vultaic]/v_mysql/mysql_server.lua` (59. sor)
- `[vultaic]/v_achievements/achievements_s.lua` (77. sor)
- `[vultaic]/v_clans/clan_server.lua` (58. sor)
- `[vultaic]/v_toptimes/toptimes_server.lua` (48. sor)

### 🛡️ Biztonsági checklist
- [ ] MySQL root jelszó megváltoztatása
- [ ] Dedikált `mtauser` MySQL felhasználó létrehozása
- [ ] Hardkódolt jelszavak frissítése minden resource-ban
- [ ] SSL tanúsítvány beállítása
- [ ] Tűzfal konfiguráció
- [ ] Admin serial-ok beállítása ACL.xml-ben

---

## 🔧 Főbb komponensek

### MTA Szerver Resource-ok
```
[admin]/          - Admin panelek és jogosultságkezelés
[sync]/           - Szinkronizációs modulok
[vultaic]/        - Fő játékmód resource-ok
├── core/         - Központi arena és játékos kezelés
├── v_mysql/      - Adatbázis kapcsolat és account kezelés
├── v_login/      - Fórum-alapú bejelentkezés
├── v_security/   - Biztonsági modulok
├── v_achievements/ - Achievement rendszer
├── v_clans/      - Klán rendszer
└── [egyéb]/      - Játékmódok és kiegészítők
```

### Adatbázis struktúra
```
v_accounts        - Éles játékos adatok
v_accounts_dev    - Fejlesztői adatok
mta_toptimes      - Legjobb idők
phpbb_forum       - Fórum adatok (phpBB esetén)
```

### Weboldal komponensek
```
/                 - Főoldal
/forum/           - Fórum (phpBB/IPB)
/stats/           - Statisztika oldal
/mta_login_test.php   - Éles login API
/mta_login_dev.php    - Dev login API
/mta_getavatar.php    - Avatar API
```

---

## 🐛 Gyakori problémák és megoldások

### Szerver nem indul
```bash
# Jogosultságok ellenőrzése
ls -la /opt/mta/mta-server64

# Port foglaltság
sudo netstat -tulpn | grep 22003

# Logok ellenőrzése
tail -f /opt/mta/mods/deathmatch/logs/server.log
```

### MySQL kapcsolat hiba
```bash
# Szolgáltatás állapota
sudo systemctl status mariadb

# Kapcsolat teszt
mysql -u mtauser -p v_accounts
```

### Bejelentkezés nem működik
```bash
# API teszt
curl -X POST http://your-domain.com/mta_login_test.php \
  -H "Content-Type: application/json" \
  -d '{"username":"test","password":"test"}'
```

---

## 📞 Támogatás és közösség

### Hasznos linkek
- **MTA:SA hivatalos oldal:** https://mtasa.com/
- **MTA Wiki:** https://wiki.multitheftauto.com/
- **phpBB:** https://www.phpbb.com/
- **Nginx dokumentáció:** https://nginx.org/en/docs/

### Logfájlok helye
```
/opt/mta/mods/deathmatch/logs/server.log     - MTA szerver log
/opt/mta/mods/deathmatch/logs/scripts.log    - Resource logok
/var/log/nginx/error.log                     - Nginx hibák
/var/log/php8.1-fpm.log                     - PHP hibák
/var/log/mysql/error.log                     - MySQL hibák
```

---

## 📝 Telepítés utáni teendők

### 1. Első bejelentkezés tesztelése
1. Regisztrálj a fórumon: `http://your-domain.com/forum`
2. Csatlakozz MTA kliensből: `your-server-ip:22003`
3. Jelentkezz be a fórum adataiddal

### 2. Admin jogosultságok beállítása
1. Szerezd meg a serial-odat: `/serial` parancs MTA-ban
2. Add hozzá az ACL.xml-hez az Admin csoportban
3. Újraindítás vagy `/aclreload` parancs

### 3. Szerver testreszabása
1. Szerver név módosítása `mtaserver.conf`-ban
2. Weboldal design testreszabása
3. Játékmódok konfigurálása

### 4. Backup beállítása
1. MySQL automatikus mentés
2. Resource fájlok mentése
3. Weboldal fájlok mentése

---

## 🎉 Sikeres telepítés ellenőrzése

Ha minden rendben van, akkor:

✅ **MTA szerver fut** - Csatlakozható a `your-server-ip:22003` címen  
✅ **Weboldal elérhető** - `http://your-domain.com` működik  
✅ **Fórum működik** - Regisztráció és bejelentkezés lehetséges  
✅ **API működik** - MTA-ból be lehet jelentkezni fórum adatokkal  
✅ **Adatbázis működik** - Statisztikák mentődnek  
✅ **Admin jogok működnek** - Admin parancsok elérhetők  

---

## 📄 Licenc és jogi információk

- **MTA:SA** - Multi Theft Auto szoftver licenc feltételei szerint
- **Vultaic MGM** - Ellenőrizd a forráskód licencét
- **phpBB** - GNU General Public License v2
- **Ubuntu, Nginx, MySQL** - Megfelelő nyílt forráskódú licencek

---

**Készítette:** AI Assistant  
**Utolsó frissítés:** 2024.08.24  
**Verzió:** 1.0

*Ez a dokumentáció a Vultaic MGM rendszer teljes telepítését mutatja be. Kérdések esetén ellenőrizd a részletes útmutatókat vagy kérj segítséget a közösségtől.*
