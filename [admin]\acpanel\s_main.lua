--[[
	VULTAIC MGM - ANTI-CHEAT CONTROL PANEL SZERVER OLDAL

	Ez a fájl az Anti-Cheat Control Panel szerver oldali logikáját tartalmazza.

	Főbb funkciók:
	- AC panel hozzáférés kezelése
	- Jogosultság ellenőrzés
	- Billentyű binding (O gomb)
	- Játékos állapot követése
	- Login/logout kezelése

	Hozzáférés:
	- Admin jogosultság szükséges
	- Automatikus billentyű binding
	- Valós idejű jogosultság frissítés
]]

-- Játékosok korábbi jogosultság állapota
local wasAllowedList = {}

--[[
	RESOURCE INDÍTÁS KEZELÉSE

	Ellenőrzi a jogosultságokat és frissíti az összes online játékost.
]]
addEventHandler ( "onResourceStart", resourceRoot,
	function ()
		doesResourceHasPermissions()                    -- Jogosultságok ellenőrzése
		for _,plr in ipairs(getElementsByType("player")) do
			updatePlayer(plr)                           -- Minden játékos frissítése
		end
	end
)

--[[
	JÁTÉKOS CSATLAKOZÁS KEZELÉSE

	Új játékos csatlakozásakor frissíti a jogosultságokat.
]]
addEventHandler ( "onPlayerJoin", root,
	function ()
		updatePlayer(source)
	end
)

--[[
	JÁTÉKOS KILÉPÉS KEZELÉSE

	Játékos kilépésekor törli a cache-elt adatokat.
]]
addEventHandler ( "onPlayerQuit", root,
	function ()
		wasAllowedList[source] = nil                    -- Cache törlése
	end
)

--[[
	JÁTÉKOS BEJELENTKEZÉS KEZELÉSE

	Bejelentkezés után frissíti a jogosultságokat.
]]
addEventHandler ( "onPlayerLogin", root,
	function ()
		updatePlayer(source)
	end
)

--[[
	JÁTÉKOS KIJELENTKEZÉS KEZELÉSE

	Kijelentkezés után frissíti a jogosultságokat.
]]
addEventHandler ( "onPlayerLogout", root,
	function ()
		updatePlayer(source)
	end
)

--[[
	Játékos jogosultság frissítése

	@param element player - A frissítendő játékos
]]
function updatePlayer(player)
	local oldAllowed = wasAllowedList[player]           -- Korábbi jogosultság
	local newAllowed = isPlayerAllowedHere(player)      -- Új jogosultság ellenőrzése
	wasAllowedList[player] = newAllowed                 -- Cache frissítése

	-- Ha most kapott jogosultságot
	if newAllowed and not oldAllowed then
		bindKey( player, "o", "down", "Show_AC_Panel" ) -- O gomb binding
		outputChatBox ( "Press 'o' to open your AC panel", player )
		if not bAllowGui then return end
		sendAllSettingsToClient()
		triggerClientEvent(player, 'onAcpClientInitialSettings', resourceRoot, getServerConfigSettingsToTransfer() )
	elseif not newAllowed and oldAllowed then
		triggerClientEvent(player, 'aClientAcMenu', resourceRoot, "close" )
		unbindKey( player, "o", "down", "Show_AC_Panel" )
	end
end

function showACPanel(player)
	if not isPlayerAllowedHere(player) then return false end
	if not doesResourceHasPermissions() then return end
	triggerClientEvent(player, 'aClientAcMenu', resourceRoot, "toggle" )
end
addCommandHandler('acp',showACPanel)
addCommandHandler('Show_AC_Panel',showACPanel)


function getServerConfigSettingsToTransfer()
	local result = {}
	local settingNameList = { "disableac", "enablesd", "verifyclientsettings" }
	for _,name in ipairs(settingNameList) do
		local value = getServerConfigSetting(name)
		result[name] = value
	end

	local verifyFlags = getServerConfigSetting( "verifyclientsettings" )

	verifyFlags = -1-verifyFlags

	local stringresult = ""
	for i=1,32 do
		local isset = math.hasbit(verifyFlags, math.bit(i))
		stringresult = stringresult .. ( isset and "1" or "0" )
	end

	result["verifyclientsettingsstring"] = stringresult

	return result
end


--------------------------------------------------------------------
-- Check this resource can do stuff
--------------------------------------------------------------------
function doesResourceHasPermissions()
	if hasObjectPermissionTo( getThisResource(), "function.kickPlayer" ) and
	   hasObjectPermissionTo( getThisResource(), "function.setServerConfigSetting" ) and
	   hasObjectPermissionTo( getThisResource(), "function.fetchRemote" ) then
		bResourceHasPermissions = true
	end

	if not bDoneFirstCheck then
		bDoneFirstCheck = true
		if bResourceHasPermissions then
			bAllowGui = true
			return true
		end
	end

	if bAllowGui then
		return true
	end

	if not bResourceHasPermissions then
		outputChatBox( "AC Panel can not start until this command is run:" )
		outputChatBox( "aclrequest allow acpanel all" )
	else
		outputChatBox( "Please restart AC Panel" )
	end
	return false
end



--------------------------------------------------------------------
-- Check player can do stuff
--------------------------------------------------------------------
function isPlayerAllowedHere(player)
	local admingroup = get("admingroup") or "Admin"
	if not isPlayerInACLGroup(player, tostring(admingroup) ) then
		return false
	end
	return true
end

function isPlayerInACLGroup(player, groupName)
	local account = getPlayerAccount(player)
	if not account then
		return false
	end
	local accountName = getAccountName(account)
	for _,name in ipairs(split(groupName,',')) do
		local group = aclGetGroup(name)
		if group then
			for i,obj in ipairs(aclGroupListObjects(group)) do
				if obj == 'user.' .. accountName or obj == 'user.*' then
					return true
				end
			end
		end
	end
	return false
end
