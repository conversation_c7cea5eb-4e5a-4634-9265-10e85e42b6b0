# Vultaic MGM - Linux VPS Szerver Telepítési Útmutató

## Tartalomjegyzék
1. [Előkészületek](#előkészületek)
2. [Linux VPS alapbeállítások](#linux-vps-alapbeállítások)
3. [Szükséges csomagok telepítése](#szükséges-csomagok-telepítése)
4. [MTA szerver telepítése](#mta-szerver-telepítése)
5. [MySQL/MariaDB telepítése és beállítása](#mysqlmariadb-telepítése-és-beállítása)
6. [Vultaic MGM resource-ok feltöltése](#vultaic-mgm-resource-ok-feltöltése)
7. [Alapkonfiguráció](#alapkonfiguráció)
8. [Tűzfal beállítások](#tűzfal-beállítások)
9. [Szerver indítás és ellenőrzés](#szerver-indítás-és-ellenőrzés)
10. [Hibaelhárítás](#hibaelhárítás)

---

## Előkészületek

### Szükséges információk
- **VPS specifikációk:** Minimum 2GB RAM, 2 CPU core, 20GB SSD
- **Operációs rendszer:** Ubuntu 20.04/22.04 LTS (ajánlott)
- **SSH hozzáférés:** root vagy sudo jogosultságokkal
- **Domain/IP cím:** A szerver eléréséhez
- **Portok:** 22003 (MTA), 22105 (HTTP), 80/443 (weboldal)

### Előkészítendő adatok
- MySQL root jelszó
- MTA szerver admin serial-ok
- Domain név (ha van)

---

## Linux VPS alapbeállítások

### 1. Rendszer frissítése
```bash
# Kapcsolódás SSH-val
ssh root@your-server-ip

# Rendszer frissítése
sudo apt update && sudo apt upgrade -y

# Időzóna beállítása
sudo timedatectl set-timezone Europe/Budapest
```

### 2. Alapvető biztonsági beállítások
```bash
# Új felhasználó létrehozása (opcionális)
sudo adduser mtaserver
sudo usermod -aG sudo mtaserver

# SSH kulcs alapú hitelesítés beállítása (ajánlott)
# Helyi gépen:
# ssh-keygen -t rsa -b 4096
# ssh-copy-id mtaserver@your-server-ip
```

### 3. Swap file létrehozása (ha szükséges)
```bash
# 2GB swap file létrehozása
sudo fallocate -l 2G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile

# Állandó beállítás
echo '/swapfile none swap sw 0 0' | sudo tee -a /etc/fstab
```

---

## Szükséges csomagok telepítése

### 1. Alapvető fejlesztői eszközök
```bash
sudo apt install -y \
    build-essential \
    git \
    wget \
    curl \
    unzip \
    screen \
    htop \
    nano \
    vim
```

### 2. MTA szerver függőségek
```bash
sudo apt install -y \
    libncurses5 \
    libreadline-dev \
    libssl-dev \
    libxml2-dev \
    libsqlite3-dev \
    zlib1g-dev \
    libcurl4-openssl-dev \
    libc6:i386 \
    libstdc++6:i386
```

### 3. Webszerver és PHP (fórumhoz)
```bash
sudo apt install -y \
    nginx \
    php8.1-fpm \
    php8.1-mysql \
    php8.1-curl \
    php8.1-gd \
    php8.1-xml \
    php8.1-mbstring \
    php8.1-zip \
    php8.1-json \
    php8.1-intl
```

---

## MTA szerver telepítése

### 1. MTA szerver letöltése
```bash
# Munkakönyvtár létrehozása
sudo mkdir -p /opt/mta
cd /tmp

# Legújabb MTA szerver letöltése (ellenőrizd a verziót!)
wget https://linux.mtasa.com/dl/multitheftauto_linux_x64-1.6.0.tar.gz

# Kicsomagolás
tar -xzf multitheftauto_linux_x64-1.6.0.tar.gz
sudo mv multitheftauto_linux_x64-1.6.0/* /opt/mta/
```

### 2. Jogosultságok beállítása
```bash
sudo chown -R mtaserver:mtaserver /opt/mta
sudo chmod +x /opt/mta/mta-server64
```

### 3. Első indítás (konfiguráció generálásához)
```bash
cd /opt/mta
sudo -u mtaserver ./mta-server64

# Néhány másodperc után leállíthatod (Ctrl+C)
# Ez létrehozza az alapvető konfigurációs fájlokat
```

---

## MySQL/MariaDB telepítése és beállítása

### 1. MariaDB telepítése
```bash
sudo apt install -y mariadb-server mariadb-client

# MariaDB indítása és engedélyezése
sudo systemctl start mariadb
sudo systemctl enable mariadb
```

### 2. MariaDB biztonságos beállítása
```bash
sudo mysql_secure_installation

# Válaszok:
# Enter current password for root: [ENTER]
# Set root password? [Y/n]: Y
# New password: [ERŐS JELSZÓ]
# Remove anonymous users? [Y/n]: Y
# Disallow root login remotely? [Y/n]: Y
# Remove test database? [Y/n]: Y
# Reload privilege tables? [Y/n]: Y
```

### 3. MySQL konfigurálása
```bash
# MySQL konfiguráció szerkesztése
sudo nano /etc/mysql/mariadb.conf.d/50-server.cnf

# Keresendő és módosítandó sorok:
# bind-address = 127.0.0.1
# max_connections = 200
# innodb_buffer_pool_size = 512M

# Újraindítás
sudo systemctl restart mariadb
```

### 4. Alapvető adatbázis létrehozása
```bash
sudo mysql -u root -p

# MySQL parancsok:
CREATE DATABASE v_accounts CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE v_accounts_dev CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'mtauser'@'localhost' IDENTIFIED BY 'MTA_Strong_Password_2024!';
GRANT ALL PRIVILEGES ON v_accounts.* TO 'mtauser'@'localhost';
GRANT ALL PRIVILEGES ON v_accounts_dev.* TO 'mtauser'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

---

## Vultaic MGM resource-ok feltöltése

### 1. Resource-ok feltöltése
```bash
# Ha Git repository-ból:
cd /opt/mta/mods/deathmatch/resources/
sudo git clone https://github.com/your-repo/Vultaic-MGM-master.git

# Vagy SCP/SFTP-vel feltöltés után:
sudo unzip Vultaic-MGM-master.zip -d /opt/mta/mods/deathmatch/resources/

# Jogosultságok beállítása
sudo chown -R mtaserver:mtaserver /opt/mta/mods/deathmatch/resources/
```

### 2. Resource struktúra ellenőrzése
```bash
ls -la /opt/mta/mods/deathmatch/resources/

# Elvárt könyvtárak:
# [admin]/
# [sync]/
# [vultaic]/
# acl.xml
# vultaic_mtaserver.conf
```

---

## Alapkonfiguráció

### 1. MTA szerver konfiguráció
```bash
cd /opt/mta/mods/deathmatch/

# Eredeti konfiguráció biztonsági mentése
sudo cp mtaserver.conf mtaserver.conf.backup

# Vultaic konfiguráció másolása
sudo cp resources/vultaic_mtaserver.conf mtaserver.conf

# Konfiguráció szerkesztése
sudo nano mtaserver.conf
```

**Fontos beállítások a mtaserver.conf-ban:**
```xml
<servername>Your Vultaic Server Name</servername>
<serverport>22003</serverport>
<httpport>22005</httpport>
<maxplayers>64</maxplayers>
<password></password>  <!-- Üres = nyilvános szerver -->
```

### 2. ACL konfiguráció
```bash
# ACL fájl másolása
sudo cp resources/acl.xml acl.xml

# Admin serial-ok hozzáadása (lásd: 04_Konfiguracio_es_Teszteles.md)
```

---

## Tűzfal beállítások

### 1. UFW tűzfal telepítése és konfigurálása
```bash
sudo apt install ufw

# Alapértelmezett szabályok
sudo ufw default deny incoming
sudo ufw default allow outgoing

# SSH engedélyezése
sudo ufw allow ssh

# MTA szerver portok
sudo ufw allow 22003/tcp
sudo ufw allow 22003/udp
sudo ufw allow 22005/tcp

# Webszerver portok
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# Tűzfal aktiválása
sudo ufw enable
sudo ufw status
```

---

## Szerver indítás és ellenőrzés

### 1. Screen session létrehozása
```bash
# Screen telepítése (ha nincs)
sudo apt install screen

# MTA szerver indítása screen-ben
cd /opt/mta
sudo -u mtaserver screen -S mta-server ./mta-server64

# Screen-ből kilépés: Ctrl+A, majd D
# Visszacsatlakozás: screen -r mta-server
```

### 2. Systemd service létrehozása (opcionális)
```bash
sudo nano /etc/systemd/system/mta-server.service
```

Service fájl tartalma:
```ini
[Unit]
Description=MTA Server
After=network.target mysql.service

[Service]
Type=simple
User=mtaserver
WorkingDirectory=/opt/mta
ExecStart=/opt/mta/mta-server64
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

```bash
# Service engedélyezése
sudo systemctl daemon-reload
sudo systemctl enable mta-server
sudo systemctl start mta-server

# Státusz ellenőrzése
sudo systemctl status mta-server
```

### 3. Logok ellenőrzése
```bash
# MTA szerver logok
tail -f /opt/mta/mods/deathmatch/logs/server.log
tail -f /opt/mta/mods/deathmatch/logs/scripts.log

# MySQL kapcsolat ellenőrzése a logokban
grep -i mysql /opt/mta/mods/deathmatch/logs/scripts.log
```

---

## Hibaelhárítás

### Gyakori problémák és megoldások

#### 1. MTA szerver nem indul
```bash
# Jogosultságok ellenőrzése
ls -la /opt/mta/mta-server64

# Függőségek ellenőrzése
ldd /opt/mta/mta-server64

# Port foglaltság ellenőrzése
sudo netstat -tulpn | grep 22003
```

#### 2. MySQL kapcsolódási hiba
```bash
# MySQL szolgáltatás státusza
sudo systemctl status mariadb

# MySQL log ellenőrzése
sudo tail -f /var/log/mysql/error.log

# Kapcsolat tesztelése
mysql -u mtauser -p -h localhost v_accounts
```

#### 3. Resource betöltési hibák
```bash
# Resource jogosultságok
sudo chown -R mtaserver:mtaserver /opt/mta/mods/deathmatch/resources/

# Meta.xml fájlok ellenőrzése
find /opt/mta/mods/deathmatch/resources/ -name "meta.xml" -exec xmllint {} \;
```

#### 4. Port elérhetőségi problémák
```bash
# Tűzfal státusz
sudo ufw status

# Port tesztelés külső gépről
telnet your-server-ip 22003
```

---

## Következő lépések

A szerver alaptelepítése után folytasd a következő útmutatókkal:
- **02_Adatbazis_Beallitas.md** - Adatbázis táblák létrehozása
- **03_Weboldal_es_Forum.md** - Weboldal és fórum telepítése
- **04_Konfiguracio_es_Teszteles.md** - Végső konfiguráció és tesztelés

---

*Ez az útmutató a Vultaic MGM szerver alaptelepítését mutatja be Linux VPS-en. A következő lépésekhez kövesd a többi útmutatót sorrendben.*
