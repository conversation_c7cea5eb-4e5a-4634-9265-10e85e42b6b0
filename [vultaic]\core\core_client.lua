--[[
	VULTAIC MGM - CORE KLIENS OLDAL

	Ez a fájl a core rendszer kliens oldali funkcióit tartalmazza:
	- Arena csatlakozás/elhagyás kezelése
	- Spectate rendszer (nézői mód)
	- Ghostmode (áthaladás más járműveken)
	- Dimenzi<PERSON>
	- Kamera és HUD kezelés

	Fő objektumok:
	- core.spectate: Spectate rendszer kezelése
	- core.movePlayerAway: Halott játékosok elrejtése
]]

-- Core objektum inicializálása kliens oldalon
core = {}
-- Spectate rendszer timer objektumai
core.spectate = {startTimer = Timer:create(), tickTimer = Timer:create()}
-- Játékos elrejtés rendszer timer objektuma
core.movePlayerAway = {readyTimer = Timer:create()}

--[[
	Core resource indítás kezelése

	Amikor a core resource elindul a kliens oldalon:
	- Eltávolítjuk az esetleges betöltött mapot
	- Bekapcsoljuk a kamerát
	- Kikapcsoljuk a HUD elemeket (csak a célkereszt marad)
	- Kiváltjuk a core indítás eventet
]]
addEventHandler("onClientResourceStart", resourceRoot,
function()
	triggerEvent("unloadMap", localPlayer)
	fadeCamera(true)
	setCameraClip(false, false)
	showPlayerHudComponent("all", false)
	showPlayerHudComponent("crosshair", true)
	triggerEvent("core:onClientResourceStart", localPlayer)
end)

--[[
	Core resource leállítás kezelése

	Amikor a core resource leáll, leállítjuk a spectate rendszert.
]]
addEventHandler("onClientResourceStop", resourceRoot,
function()
	core.spectate.stop()
end)

--[[
	Arena csatlakozás kezelése kliens oldalon

	Ez az event akkor aktiválódik, amikor a játékos sikeresen csatlakozott egy arenához.
	A szerver oldal küldi ezt az eventet az arena adataival.
]]
addEvent("core:handleClientJoinArena", true)
addEventHandler("core:handleClientJoinArena", resourceRoot,
function(data)
	core.arenaData = {}
	-- Arena adatok szinkronizálása
	for i, v in pairs(data) do
		core.arenaData[i] = v
	end
	fadeCamera(true)
	-- Dimenzió frissítése
	setElementDimension(localPlayer, core.arenaData.dimension)
	core.updateAllDimensions()
	-- Kiegészítő funkciók beállítása
	setGhostmodeEnabled(core.arenaData.ghostmodeEnabled)
	triggerEvent("core:onClientJoinArena", localPlayer, core.arenaData)
	-- Segítő üzenet megjelenítése
	triggerEvent("notification:create", localPlayer, "Gyors Útmutató", "Tartsd nyomva az 'F1'-et az arena elhagyásához")
end)

--[[
	Arena elhagyás kezelése kliens oldalon

	Ez az event akkor aktiválódik, amikor a játékos elhagyja az arenát.
	A szerver oldal küldi ezt az eventet.
]]
addEvent("core:handleClientLeaveArena", true)
addEventHandler("core:handleClientLeaveArena", resourceRoot,
function()
	-- Arena adatok törlése
	core.arenaData = nil
	-- Dimenzió visszaállítása lobby-ra (0)
	setElementDimension(localPlayer, 0)
	-- Kiegészítő funkciók kikapcsolása
	core.spectate.stop()
	setGhostmodeEnabled(false)
	triggerEvent("core:onClientLeaveArena", localPlayer)
	-- HUD és játékos állapot visszaállítása
	setElementFrozen(localPlayer, true)
	showPlayerHudComponent("all", false)
	showPlayerHudComponent("crosshair", true)
end)

--[[
	DIMENZIÓ JAVÍTÁSOK ÉS SEGÉDFÜGGVÉNYEK
]]

--[[
	Összes dimenzió frissítése

	Ez a függvény biztosítja, hogy az arena összes játékosa
	a megfelelő dimenzióban legyen. Javítja a szinkronizációs problémákat.
]]
function core.updateAllDimensions()
	local dimension = core.arenaData and core.arenaData.dimension or getElementDimension(localPlayer)
	local parent = getElementParent(localPlayer)
	for i, player in pairs(getElementChildren(parent, "player")) do
		setElementDimension(player, dimension)
	end
end

--[[
	HASZNOS SEGÉDFÜGGVÉNYEK

	Ezek a függvények megkönnyítik az arena és játékos információk lekérdezését.
]]

--[[
	Ellenőrzi, hogy a kliens a lobbiban van-e

	@return boolean - true ha lobbiban van, false ha arenában
]]
function isClientInLobby()
	return getElementData(localPlayer, "arena") == nil or getElementData(localPlayer, "arena") == "lobby" and true or false
end

--[[
	Kliens arena element lekérdezése

	@return element - Az arena element
]]
function getClientArena()
	return core.arenaData.element or getElementParent(localPlayer)
end

--[[
	Játékos arena element lekérdezése

	@param element player - A játékos
	@return element - A játékos arena elementje
]]
function getPlayerArena(player)
	return getElementParent(player)
end

--[[
	Kliens arena adatok lekérdezése

	@return table - Az arena adatok táblája
]]
function getClientArenaData()
	return core.arenaData or {}
end

--[[
	Kliens arena játékosainak lekérdezése

	@return table - A játékosok táblája
]]
function getClientArenaPlayers()
	local parent = getElementParent(localPlayer)
	local players = parent and getElementChildren(parent, "player") or {}
	return players
end

--[[
	Kliens arena járműveinek lekérdezése

	@return table - A járművek táblája

	Ez a függvény összegyűjti az arena összes játékosának járművét.
]]
function getClientArenaVehicles()
	local vehicles = {}
	local parent = getElementParent(localPlayer)
	if parent then
		for i, player in pairs(getElementChildren(parent, "player")) do
			local vehicle = getPedOccupiedVehicle(player)
			if isElement(vehicle) then
				table.insert(vehicles, vehicle)
			end
		end
	end
	return vehicles
end

--[[
	Ellenőrzi, hogy egy játékos halott-e

	@param element player - A játékos
	@return boolean - true ha halott, false ha él

	Ez a függvény több módszerrel is ellenőrzi a játékos állapotát.
]]
function isPlayerDead(player)
	return not getElementHealth(player) or getElementHealth(player) < 1e-45 or getElementHealth(player) <= 0 or isPedDead(player)
end

--[[
	GHOSTMODE RENDSZER

	A ghostmode lehetővé teszi, hogy a járművek áthaladjon egymáson
	ütközés nélkül. Ez hasznos race módokban a fair play biztosításához.
]]

-- Ghostmode állapot tárolása
local ghostmodeEnabled = false

--[[
	Ghostmode be/kikapcsolása

	@param boolean enabled - true = bekapcsolva, false = kikapcsolva

	Amikor a ghostmode aktív, a járművek nem ütköznek egymással.
]]
function setGhostmodeEnabled(enabled)
	ghostmodeEnabled = enabled and true or false
	updateElementCollisions()
end

--[[
	Element ütközések frissítése

	@param element element - Specifikus element (opcionális)

	Ez a függvény frissíti az ütközési beállításokat a ghostmode állapotának megfelelően.
	Ha nincs megadva element, akkor az összes arena járművet frissíti.
]]
function updateElementCollisions(element)
	if isElement(element) then
		-- Specifikus element ütközésének beállítása
		local collidable = ghostmodeEnabled == false
		for _, other in pairs(getClientArenaVehicles()) do
			setElementCollidableWith(element, other, collidable)
		end
	else
		-- Összes arena jármű ütközésének beállítása
		local collidable = ghostmodeEnabled == false
		for _, element in pairs(getClientArenaVehicles()) do
			for _, other in pairs(getClientArenaVehicles()) do
				setElementCollidableWith(element, other, collidable)
			end
		end
	end
end

--[[
	SPECTATE RENDSZER (NÉZŐI MÓD)

	Ez a rendszer lehetővé teszi a játékosoknak, hogy más játékosokat nézzenek
	amikor meghalnak vagy manuálisan aktiválják a spectate módot.
]]

--[[
	Spectate mód indítása

	@param boolean isManual - true ha manuálisan aktiválták, false ha automatikusan (halál után)

	Ez a függvény elindítja a spectate módot:
	- Elmenti a jelenlegi jármű adatait
	- Beállítja a spectate célpontokat
	- Elindítja a timerek-et
]]
function core.spectate.start(isManual)
	if core.spectate.active then
		core.spectate.stop()
	end
	core.spectate.active = true
	core.spectate.targets = core.spectate.getTargets()
	core.spectate.target = nil

	-- Jármű adatok mentése a későbbi visszaállításhoz
	if isElement(core.vehicle) then
		core.spectate.savedData = {
			model = getElementModel(core.vehicle),
			position = {getElementPosition(core.vehicle)},
			rotation = {getElementRotation(core.vehicle)},
			velocity = {getElementVelocity(core.vehicle)},
			turnVelocity = {getVehicleTurnVelocity(core.vehicle)},
			health = math.max(getElementHealth(core.vehicle), 0)
		}
	end

	core.spectate.currentIndex = 1
	if isManual then
		core.spectate._start()
	else
		-- Kis késleltetés automatikus spectate esetén
		core.spectate.startTimer:setTimer(core.spectate._start, 500, 1)
	end
	setCameraMatrix(getCameraMatrix())
	fadeCamera(true)
	-- Rendszeres ellenőrzés timer indítása
	core.spectate.tickTimer:setTimer(core.spectate.tick, 1000, 0)
end
-- Event regisztráció és alias függvény
addEvent("spectate:start", true)
addEventHandler("spectate:start", resourceRoot, core.spectate.start)
startSpectating = core.spectate.start

--[[
	Spectate navigáció billentyűkkel

	Bal/jobb nyíl billentyűkkel lehet váltani a spectate célpontok között.
]]
addEventHandler("onClientKey", root,
function(button, press)
	if core.spectate.active and press then
		if button == "arrow_l" then
			core.spectate.previous()
		elseif button == "arrow_r" then
			core.spectate.next()
		end
	end
end)

--[[
	Spectate célpontok lekérdezése

	@return table - Az élő játékosok táblája (kivéve a helyi játékost)

	Ez a függvény összegyűjti az összes élő játékost az arenában,
	akiket spectate-elni lehet.
]]
function core.spectate.getTargets()
	local targets = {}
	for i, player in pairs(getClientArenaPlayers()) do
		if player ~= localPlayer and getElementData(player, "state") == "alive" then
			table.insert(targets, player)
		end
	end
	return targets
end

--[[
	Spectate célpont érvényességének ellenőrzése

	@param element player - A játékos
	@return boolean - true ha érvényes célpont, false ha nem
]]
function core.spectate.isValidTarget(player)
	return isElement(player) and tableFind(core.spectate.targets, player) and true or false
end

--[[
	Spectate mód tényleges indítása

	@param element target - Specifikus célpont (opcionális)

	Ez a belső függvény végzi el a spectate mód tényleges beállítását.
]]
function core.spectate._start(target)
	if target then
		if not core.spectate.isValidTarget(target) then
			core.spectate.setTarget(false)
		end
		return
	end
	-- Játékos elrejtése és spectate célpont beállítása
	core.movePlayerAway.start()
	core.spectate.setTarget(core.spectate.targets[core.spectate.currentIndex])
end

--[[
	Spectate mód leállítása

	@param boolean isManual - true ha manuálisan állították le, false ha automatikusan

	Ez a függvény leállítja a spectate módot és visszaállítja a normál nézetet.
]]
function core.spectate.stop(isManual)
	if not core.spectate.active then
		return
	end
	-- Spectate állapot törlése
	core.spectate.active = false
	core.spectate.manual = false
	core.spectate.targets = {}
	core.spectate.target = nil
	-- Timerek leállítása
	core.spectate.startTimer:killTimer()
	core.spectate.tickTimer:killTimer()
	core.movePlayerAway.readyTimer:killTimer()
	-- Kamera visszaállítása
	if isManual then
		core.movePlayerAway.stop()
	else
		setCameraTarget(localPlayer)
	end
end
-- Event regisztráció és alias függvény
addEvent("spectate:stop", true)
addEventHandler("spectate:stop", resourceRoot, core.spectate.stop)
stopSpectating = core.spectate.stop

--[[
	Spectate mód kényszerített leállítása

	Ez a függvény azonnal leállítja a spectate módot minden ellenőrzés nélkül.
	Általában resource leállításkor vagy kritikus hibák esetén használják.
]]
function core.spectate.forcedStop()
	core.spectate.active = false
	core.spectate.manual = false
	core.spectate.targets = {}
	core.spectate.startTimer:killTimer()
	core.spectate.tickTimer:killTimer()
	setCameraMatrix(getCameraMatrix())
end
forcedStopSpectating = core.spectate.forcedStop

--[[
	Spectate célpont beállítása

	@param element|nil target - A célpont játékos vagy nil

	Ez a függvény beállítja a spectate kamera célpontját.
	Ha nincs célpont, akkor felülnézeti kamerát állít be.
]]
function core.spectate.setTarget(target)
	core.spectate.target = target
	if isElement(target) then
		-- Ellenőrizzük, hogy nem ugyanaz a célpont-e
		if target == localPlayer or target == core.spectate.getTarget() then
			return
		end
		-- Kamera beállítása a célpontra
		setCameraTarget(target)
	else
		-- Felülnézeti kamera beállítása ha nincs célpont
		local x, y, z = getCameraMatrix()
		x = x - (x % 32)  -- Rács pozícióra igazítás
		y = y - (y % 32)
		z = getGroundPosition(x, y, 5000) or 40
		setCameraTarget(localPlayer)
		setCameraMatrix(x, y, z + 10, x, y + 50, z + 60)
	end
end

--[[
	Jelenlegi spectate célpont lekérdezése

	@return element|nil - A jelenlegi célpont játékos

	Ez a függvény visszaadja a jelenlegi spectate célpontot.
	Ha járművet néz, akkor a járművezető játékost adja vissza.
]]
function core.spectate.getTarget()
	local target = getCameraTarget()
	if isElement(target) and getElementType(target) == "vehicle" then
		target = getVehicleOccupant(target)
	end
	return target
end

--[[
	Előző spectate célpontra váltás

	Ez a függvény az előző játékosra vált a spectate listában.
	Ha az első játékosnál van, akkor az utolsóra ugrik.
]]
function core.spectate.previous()
	if not core.spectate.active or core.spectate.startTimer:isActive() then
		return
	end
	core.spectate.currentIndex = core.spectate.currentIndex - 1
	if core.spectate.currentIndex < 1 then
		core.spectate.currentIndex = #core.spectate.targets
	end
	core.spectate.setTarget(core.spectate.targets[core.spectate.currentIndex])
end

--[[
	Következő spectate célpontra váltás

	Ez a függvény a következő játékosra vált a spectate listában.
	Ha az utolsó játékosnál van, akkor az elsőre ugrik.
]]
function core.spectate.next()
	if not core.spectate.active or core.spectate.startTimer:isActive() then
		return
	end
	core.spectate.currentIndex = core.spectate.currentIndex + 1
	if core.spectate.currentIndex > #core.spectate.targets then
		core.spectate.currentIndex = 1
	end
	core.spectate.setTarget(core.spectate.targets[core.spectate.currentIndex])
end

--[[
	Spectate rendszer rendszeres ellenőrzése

	Ez a függvény másodpercenként fut és ellenőrzi a spectate állapotot.
	Automatikusan vált célpontot, ha a jelenlegi érvénytelen lett.
]]
function core.spectate.tick()
	if not core.spectate.active or core.spectate.startTimer:isActive() then
		return
	end
	local target = core.spectate.target
	-- Ellenőrizzük, hogy a célpont még érvényes-e
	if target and core.spectate.isValidTarget(target) and core.spectate.getTarget() ~= target then
		setCameraTarget(target)
	elseif (not target or not core.spectate.isValidTarget(target)) and #core.spectate.targets > 0 then
		-- Ha nincs érvényes célpont, váltunk a következőre
		core.spectate.target = nil
		core.spectate.next()
	end
end

--[[
	JÁTÉKOS ELREJTÉS RENDSZER

	Ez a rendszer elrejti a halott játékosokat és járműveket,
	hogy ne zavarják a spectate nézetet.
]]

--[[
	Halott játékos elrejtésének indítása

	Ez a "hős függvény" elrejti a halott játékost és járművét:
	- Áthelyezi őket a térkép alá
	- Befagyasztja őket
	- Kijavítja és védetté teszi a járművet
	- Visszaállítja a játékos életét
]]
function core.movePlayerAway.start()
	local cameraTarget = core.spectate.getTarget()
	-- Játékos elrejtése ha nincs járműben
	if not isPedInVehicle(localPlayer) then
		setElementPosition(localPlayer, 0, 0, -10)
		setElementFrozen(localPlayer, true)
	end
	-- Jármű elrejtése és beállítása
	if isElement(core.vehicle) then
		setElementVelocity(core.vehicle, 0, 0, 0)
		setVehicleTurnVelocity(core.vehicle, 0, 0, 0)
		setElementPosition(core.vehicle, 0, 0, -15)  -- Térkép alá
		setElementRotation(core.vehicle, 0, 0, 0)
		setElementFrozen(core.vehicle, true)
		fixVehicle(core.vehicle)  -- Jármű javítása
		setElementCollisionsEnabled(core.vehicle, false)  -- Ütközés kikapcsolása
		setVehicleDamageProof(core.vehicle, true)  -- Sérülés védelem
	end
	-- Játékos életének visszaállítása
	setElementHealth(localPlayer, 100)
	-- Kamera célpont visszaállítása
	if isElement(cameraTarget) and core.spectate.getTarget() ~= cameraTarget then
		setCameraTarget(cameraTarget)
	end
end

--[[
	Halott játékos visszaállítása

	Ez a függvény visszaállítja a játékost és járművét az eredeti állapotba
	amikor a spectate mód véget ér.
]]
function core.movePlayerAway.stop()
	if isElement(core.vehicle) and core.spectate.savedData then
		-- Jármű eredeti állapotának visszaállítása
		setElementModel(core.vehicle, core.spectate.savedData.model)
		setElementPosition(core.vehicle, unpack(core.spectate.savedData.position))
		setElementRotation(core.vehicle, unpack(core.spectate.savedData.rotation))
		setElementHealth(core.vehicle, core.spectate.savedData.health)
		setCameraTarget(localPlayer)

		-- Késleltetett visszaállítás timer indítása
		core.movePlayerAway.readyTimer:setTimer(function()
			local cameraTarget = core.spectate.getTarget()
			if cameraTarget == localPlayer and isPedInVehicle(localPlayer) then
				if isElement(core.vehicle) and core.spectate.savedData then
					-- Jármű fizikai tulajdonságainak visszaállítása
					setElementCollisionsEnabled(core.vehicle, true)
					setElementFrozen(core.vehicle, false)
					setElementVelocity(core.vehicle, unpack(core.spectate.savedData.velocity))
					setVehicleTurnVelocity(core.vehicle, unpack(core.spectate.savedData.turnVelocity))
				end
				core.spectate.savedData = nil
				-- Szerver értesítése a befagyasztás feloldásáról
				triggerServerEvent("spectate:unfreeze", localPlayer)
				core.movePlayerAway.readyTimer:killTimer()
			end
		end, 500, 0)
	end
end

--[[
	FÜGGVÉNY FELÜLÍRÁSOK ÉS JAVÍTÁSOK
]]

--[[
	setCameraTarget függvény felülírása

	Az eredeti setCameraTarget függvény felülírása dimenzió kezeléssel.
	Ez biztosítja, hogy a kamera célpontja a megfelelő dimenzióban legyen.
]]
_setCameraTarget = setCameraTarget
setCameraTarget = function(target)
	if isElement(target) then
		-- Célpont dimenzió szinkronizálása a játékossal
		setElementDimension(target, getElementDimension(localPlayer))
		_setCameraTarget(target)
		-- Spectate célpont adatának beállítása
		setElementData(localPlayer, "spectatedTarget", target, false)
		-- Custom event kiváltása
		triggerEvent("core:onClientCameraTargetChange", localPlayer, target)
	end
end

--[[
	EVENT KEZELŐK

	Ezek az event kezelők biztosítják a core rendszer megfelelő működését.
]]

--[[
	Element stream in kezelése

	Amikor egy játékos vagy jármű betöltődik, frissítjük az ütközési beállításokat.
]]
addEventHandler("onClientElementStreamIn", root,
function()
	if getElementType(source) == "player" or getElementType(source) == "vehicle" then
		updateElementCollisions(source)
	end
end)

--[[
	Jármű belépés kezelése

	Amikor a helyi játékos belép egy járműbe, eltároljuk a referenciát.
]]
addEventHandler("onClientPlayerVehicleEnter", root,
function(vehicle)
	if source == localPlayer then
		core.vehicle = vehicle
	end
end)

--[[
	Element adat változás kezelése

	Ez az event kezelő figyeli a játékosok állapotváltozásait és
	automatikusan frissíti a spectate célpontokat.
]]
addEventHandler("onClientElementDataChange", root,
function(dataName)
	if getElementType(source) ~= "player" or source == localPlayer or not core.spectate.active then
		return
	end

	-- Játékos arena elhagyása
	if dataName == "arena" and getPlayerArena(source) ~= getClientArena() then
		tableRemove(core.spectate.targets, source)
		if source == core.spectate.target then
			core.spectate.target = nil
			setCameraMatrix(getCameraMatrix())
			core.spectate.next()
		end
	-- Játékos állapot változása (élet/halál)
	elseif dataName == "state" and getPlayerArena(source) == getClientArena() then
		local state = getElementData(source, "state")
		if state == "alive" then
			-- Élő játékos hozzáadása a spectate listához
			tableInsert(core.spectate.targets, source)
			if not core.spectate.target or not core.spectate.getTarget() then
				setCameraMatrix(getCameraMatrix())
				-- Index megkeresése és beállítása
				for i, player in pairs(core.spectate.targets) do
					if player == source then
						core.spectate.currentIndex = i
						break
					end
				end
				core.spectate.target = source
				setCameraTarget(source)
			end
		elseif getPlayerArena(source) == getClientArena() then
			-- Halott játékos eltávolítása a spectate listából
			tableRemove(core.spectate.targets, source)
			if source == core.spectate.target then
				core.spectate.target = nil
				setCameraMatrix(getCameraMatrix())
				core.spectate.next()
			end
		end
	end
end)

--[[
	PARANCS KEZELŐK

	Alapvető parancsok az arena csatlakozáshoz és elhagyáshoz.
]]

--[[
	/join parancs kezelése

	Lehetővé teszi a játékosoknak, hogy paranccsal csatlakozzanak arenához.
]]
addCommandHandler("join",
function(command, arena)
	if type(arena) == "string" then
		triggerServerEvent("core:onPlayerRequestJoinArena", localPlayer, arena)
	end
end)

--[[
	/leave parancs kezelése

	Lehetővé teszi a játékosoknak, hogy paranccsal elhagyják az arenát.
]]
addCommandHandler("leave",
function(command)
	triggerServerEvent("core:onPlayerRequestLeaveArena", localPlayer)
end)