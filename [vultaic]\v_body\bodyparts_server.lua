--[[
	VULTAIC MGM - BODYPARTS (KAROSSZÉRIA RÉSZEK) RENDSZER SZERVER OLDAL

	Ez a fájl a bodyparts (karosszéria részek) rendszer szerver oldali logikáját tartalmazza.

	Főbb funkciók:
	- Bodyparts vásárlás kezelése
	- Játékos bodyparts adatok szinkronizálása
	- Bodyparts frissítés kezelése
	- Ár kezelés és validáció
	- Adatbázis integráció

	M<PERSON>ködés:
	1. Játékos megvásárolja a bodyparts jogosultságot
	2. Ezután módosíthatja a jármű karosszéria részeit
	3. A beállítások mentésre kerülnek az adatbázisba
	4. Bejelentkezéskor visszatöltődnek a beállítások
]]

--[[
	RESOURCE INDÍTÁS KEZELÉSE

	Resource indításkor betölti az árakat és frissíti minden játékos bodyparts adatait.
]]
addEventHandler("onResourceStart", resourceRoot,
function()
	-- Tuning árak betöltése a v_tuning resource-ból
	local upgradePrices, exclusiveUpgrades = exports.v_tuning:getTuningUpgrades()
	bodypartsPrice = upgradePrices["Body parts"]  -- Bodyparts ár beállítása

	-- Minden online játékos bodyparts adatainak frissítése
	for i, player in pairs(getElementsByType("player")) do
		updatePlayerBodyparts(player)
	end
end)

--[[
	Játékos bodyparts adatok frissítése

	@param element player - A játékos element

	Ez a függvény betölti és beállítja a játékos bodyparts adatait az adatbázisból.
]]
function updatePlayerBodyparts(player)
	if isElement(player) then
		-- Ellenőrzés, hogy megvásárolta-e a bodyparts jogosultságot
		local bought = getPlayerTuningStats(player, "bodyparts_bought")
		if bought then
			setElementData(player, "bodyparts_bought", true)

			-- Bodyparts adatok betöltése
			local data = getPlayerTuningStats(player, "bodyparts")
			if data then
				setElementData(player, "bodyparts", data)
			end
		end
	end
end

-- Event kezelő a játékos bejelentkezéshez
addEvent("mysql:onPlayerLogin", true)
addEventHandler("mysql:onPlayerLogin", root, function()
	updatePlayerBodyparts(source)
end)

function handleBodypartsPurchase()
	if not getElementData(source, "LoggedIn") then
		return triggerClientEvent(source, "notification:create", source, "Purchase failed", "You are not logged in")
	end
	local bought = getPlayerTuningStats(source, "bodyparts_bought")
	if bought then
		return triggerClientEvent(source, "notification:create", source, "Purchase failed", "You have already bought body parts")
	end
	local money = tonumber(getElementData(source, "money") or 0)
	if money < bodypartsPrice then
		return triggerClientEvent(source, "notification:create", source, "Purchase failed", "You don't have enough money to buy body parts")
	end
	setPlayerTuningStats(source, "bodyparts_bought", "1")
	setElementData(source, "bodyparts_bought", true)
	takePlayerStats(source, "money", bodypartsPrice)
	triggerClientEvent(source, "notification:create", source, "Purchase successful", "You have successfully bought body parts")
end
addEvent("purchaseBodyparts", true)
addEventHandler("purchaseBodyparts", root, handleBodypartsPurchase)

function handleBodypartsUpdate(data)
	if type(data) ~= "table" then
		return print("Failed to update bodyparts for "..getPlayerName(source))
	end
	local bought = getPlayerTuningStats(source, "bodyparts_bought")
	if not bought then
		return triggerClientEvent(source, "notification:create", source, "Update failed", "You didn't buy body parts")
	end
	setPlayerTuningStats(source, "bodyparts", data)
	setElementData(source, "bodyparts", data)
	print(getPlayerName(source).." has just updated his body parts")
	triggerClientEvent(source, "notification:create", source, "Updated", "You have successfully updated your body parts")
end
addEvent("updateBodyparts", true)
addEventHandler("updateBodyparts", root, handleBodypartsUpdate)

setPlayerTuningStats = function(player, stats, value, ...)
	return exports.v_mysql:setPlayerTuningStats(player, stats, value, ...)
end

getPlayerTuningStats = function(player, stats, ...)
	return exports.v_mysql:getPlayerTuningStats(player, stats, ...)
end

takePlayerStats = function(player, stats, ...)
	return exports.v_mysql:takePlayerStats(player, stats, ...)
end