--[[
	VULTAIC MGM - CORE SZERVER OLDAL

	Ez a fájl a teljes MGM rendszer magja. Itt történik:
	- Arena regisztráció és kezelés
	- Játékosok mozgatása arenák között
	- Dimenzi<PERSON> kezel<PERSON>
	- Játékos ID-k kiosztása
	- Spectate rendszer
	- Map kompatibilitás ellenőrzés

	Fő adatstruktúrák:
	- core.arenas: Összes regisztrált arena adatai
	- core.resources: Resource -> arena mapping
	- core.dimensions: Foglalt dimenziók nyilvántartása
	- core.ids: Játékos ID-k nyilvántartása
]]

-- Fő core objektum inicializálása - tartalmazza az összes arena és rendszer adatot
core = {arenas = {}, resources = {}, dimensions = {}, ids = {}}

--[[
	Következő szabad dimenzió megkeresése

	@return number - A következő szabad dimenzió száma

	Minden arenának saját dimenziója van, hogy a játékosok ne lássák egymást
	különböző arenákban. Ez a függvény megkeresi a következő szabad dimenziót.
]]
function getNextFreeDimension()
	local dimension = 1
	-- Addig növeljük a dimenzió számot, amíg szabad helyet nem találunk
	while core.dimensions[dimension] ~= nil do
		dimension = dimension + 1
	end
	return dimension
end

--[[
	Arena regisztrálása a rendszerben

	@param table settings - Arena beállítások (id, name, maximumPlayers, stb.)
	@return table|nil - Arena adatok vagy nil hiba esetén

	Ez a függvény regisztrálja az arenákat a core rendszerben.
	Minden játékmód (race, dm, fdd, stb.) ezt hívja meg az induláskor.
]]
function registerArena(settings)
	-- Először ellenőrizzük a kötelező értékeket
	local id = settings.id
	if not id then
		outputDebugString("Érvénytelen/hiányzó 'id' megadva", 0, 25, 132, 109)
		return
	end
	if type(settings.name) ~= "string" then
		outputDebugString("Érvénytelen/hiányzó 'name' megadva", 0, 25, 132, 109)
		return
	end
	-- Ha nincs megadva maximális játékosszám, akkor 0 (korlátlan)
	if type(settings.maximumPlayers) ~= "number" then
		settings.maximumPlayers = 0
	end
	-- Ellenőrizzük, hogy már létezik-e ilyen ID-jú arena
	if core.arenas[id] then
		outputDebugString("Arena duplikáció elkerülése ID-hoz "..tostring(id)..", már használatban van", 0, 25, 132, 109)
		return
	end
	-- Biztonsági változók inicializálása
	closed = closed and true or false
	password = type(password) == "string" and password or nil

	-- Arena element létrehozása az MTA element rendszerben
	local element = createElement("arena", id)
	if not isElement(element) then
		outputDebugString("Arena element létrehozása sikertelen", 0, 25, 132, 109)
		return
	end

	-- Most regisztráljuk az arenát
	local data = {}
	-- Összes beállítás átmásolása az element data-ba és a helyi data táblába
	for i, v in pairs(settings) do
		setElementData(element, i, v)
		data[i] = v
	end
	-- Resource információ hozzáadása (melyik resource regisztrálta)
	setElementData(element, "resource", sourceResource)
	data.element = element
	data.resource = sourceResource
	-- Egyedi dimenzió kiosztása az arenának
	data.dimension = getNextFreeDimension()
	setElementDimension(element, data.dimension)
	-- Dimenzió foglalásának jelzése
	core.dimensions[data.dimension] = true
	-- Arena regisztrálása a core rendszerben
	core.arenas[id] = data
	core.resources[sourceResource] = data
	-- Kliensek értesítése az új arenáról
	triggerClientEvent(root, "core:syncArenasData", root)
	outputDebugString("Arena regisztrálva ["..data.name.." - "..data.id.." - "..data.dimension.." - "..data.maximumPlayers.." "..tostring(data.closed).." "..tostring(data.password).."]", 0, 25, 132, 109)
	return data
end

--[[
	Resource leállítás kezelése

	Ha egy arena resource leáll, automatikusan eltávolítjuk az arenát a rendszerből.
	Ez biztosítja, hogy ne maradjanak "halott" arenák a listában.
]]
addEventHandler("onResourceStop", root,
function(resource)
	if core.resources[resource] then
		local data = core.resources[resource]
		unregisterArena(data.id)
	end
end)

--[[
	Core resource leállítás kezelése

	Ha a core resource áll le, minden játékost visszaküldünk a lobbiba.
	Ez biztosítja, hogy senki ne ragadjon be egy arenában.
]]
addEventHandler("onResourceStop", resourceRoot,
function()
	for i, player in pairs(getElementsByType("player")) do
		local arena = getElementData(player, "arena")
		if arena then
			removePlayerFromArena(player, arena)
			-- Játékos visszaküldése a lobbiba
			movePlayerToLobby(player)
		end
	end
end)

--[[
	Arena eltávolítása a rendszerből

	@param string id - Az eltávolítandó arena ID-ja

	Ez a függvény teljesen eltávolít egy arenát a rendszerből:
	- Minden játékost visszaküld a lobbiba
	- Felszabadítja a dimenziót
	- Törli az arena elementet
	- Értesíti a klienseket
]]
function unregisterArena(id)
	local arena = core.arenas[id]
	if arena then
		outputDebugString("Arena eltávolítása: "..arena.name, 0, 25, 132, 109)
		-- Arena eltávolítás event kiváltása
		triggerEvent("core:onArenaUnregister", arena.element)
		-- Játékosok értesítése az arena bezárásáról
		triggerClientEvent(arena.element, "notification:create", arena.element, "Arena", "Az arena bezárult, kérjük próbáld újra később")
		-- Összes játékos visszaküldése a lobbiba
		for i, player in pairs(getElementChildren(arena.element, "player")) do
			removePlayerFromArena(player, id)
			movePlayerToLobby(player)
		end
		-- Arena element megsemmisítése
		destroyElement(arena.element)
		-- Dimenzió felszabadítása
		if core.dimensions[arena.dimension] then
			core.dimensions[arena.dimension] = nil
		end
		arena = nil
	end
	-- Arena eltávolítása a core listából
	core.arenas[id] = nil
	-- Kliensek értesítése a változásról
	triggerClientEvent(root, "core:syncArenasData", root)
end

--[[
	Játékos áthelyezése egy arenába

	@param element player - A játékos element
	@param string id - A cél arena ID-ja

	Ez a függvény áthelyezi a játékost a megadott arenába:
	- Ellenőrzi az arena érvényességét
	- Beállítja a megfelelő dimenziót
	- Meghívja az arena saját movePlayerToArena függvényét
	- Értesíti a többi játékost a csatlakozásról
]]
function movePlayerToArena(player, id)
	local arena = core.arenas[id]
	if isElement(player) then
		if not arena then
			outputChatBox("Érvénytelen arenába próbáltál csatlakozni.", player, 255, 255, 255, true)
			return
		end
		-- Ellenőrizzük, hogy az arena resource fut-e
		if arena.resource and getResourceState(arena.resource) == "running" then
			-- Függőben lévő latent eventek törlése (pl. file letöltések)
			cancelLatentsForPlayer(player)
			-- Játékos áthelyezése az arena elementhez
			setElementParent(player, arena.element)
			-- Dimenzió beállítása
			setElementDimension(player, arena.dimension)
			-- Arena ID mentése a játékoshoz
			setElementData(player, "arena", arena.id)
			-- Core event kiváltása
			triggerEvent("core:onPlayerJoinArena", player, arena)
			-- Kliens értesítése az arena csatlakozásról
			triggerClientEvent(player, "core:handleClientJoinArena", resourceRoot, arena)
			-- Arena specifikus csatlakozás kezelés
			call(arena.resource, "movePlayerToArena", player)
			-- Többi játékos értesítése a csatlakozásról
			triggerClientEvent(arena.element, "notification:create", arena.element, "Arena", getPlayerName(player).." #FFFFFFcsatlakozott az arenához", "joinquit", "join")
		end
	end
end

--[[
	Játékos eltávolítása egy arenából

	@param element player - A játékos element
	@param string id - Az arena ID-ja

	Ez a függvény eltávolítja a játékost az arenából:
	- Meghívja az arena saját removePlayerFromArena függvényét
	- Kiváltja a megfelelő eventeket
	- Törli a spectate állapotot
]]
function removePlayerFromArena(player, id)
	local arena = core.arenas[id]
	if isElement(player) and arena then
		-- Arena specifikus eltávolítás kezelés
		if arena.resource and getResourceState(arena.resource) == "running" then
			call(arena.resource, "removePlayerFromArena", player)
		end
		-- Core event kiváltása
		triggerEvent("core:onPlayerLeaveArena", player, arena)
		-- Kliens értesítése az arena elhagyásról
		triggerClientEvent(player, "core:handleClientLeaveArena", resourceRoot)
	end
	-- Biztonsági intézkedés - spectate állapot törlése
	setElementData(player, "spectating", nil, false)
end

--[[
	Játékos visszaküldése a lobbiba

	@param element player - A játékos element

	Ez a függvény visszaküldi a játékost a lobbiba:
	- Eltávolítja az aktuális arenából
	- Áthelyezi a lobby-ba
]]
function sendPlayerToLobby(player)
	if isElement(player) then
		local arena = getElementData(player, "arena")
		if not arena then
			return
		end
		removePlayerFromArena(player, arena)
		-- Játékos áthelyezése a lobbiba
		movePlayerToLobby(player)
	end
end

--[[
	Játékos ID kiosztása

	@param element player - A játékos element

	Minden játékosnak egyedi numerikus ID-t oszt ki a szerver.
	Ez hasznos a spectate rendszerhez és egyéb funkciókhoz.
]]
function assignPlayerID(player)
	if isElement(player) then
		-- Belső függvény a következő szabad ID megkereséséhez
		local function getNextID()
			local id = 1
			-- Addig növeljük az ID-t, amíg szabad helyet nem találunk
			while core.ids[id] ~= nil do
				id = id + 1
			end
			return id
		end
		local id = getNextID()
		-- ID kiosztása és mentése
		core.ids[id] = player
		setElementData(player, "id", id)
	end
end

--[[
	Játékos ID felszabadítása

	@param element player - A játékos element

	Amikor a játékos kilép, felszabadítjuk az ID-ját,
	hogy újra felhasználható legyen.
]]
function deassignPlayerID(player)
	if isElement(player) then
		local id = getElementData(player, "id")
		if id and core.ids[id] then
			core.ids[id] = nil
		end
	end
end

--[[
	Játékos keresése ID alapján

	@param number id - A keresett játékos ID-ja
	@return element|nil - A játékos element vagy nil

	Ez a függvény lehetővé teszi játékosok keresését ID alapján.
	Főleg a spectate rendszerhez használatos.
]]
function getPlayerFromID(id)
	if id and core.ids[id] then
		return core.ids[id]
	end
end

--[[
	Játékos csatlakozás kezelése

	Amikor egy játékos csatlakozik a szerverre:
	- Áthelyezzük a lobbiba
	- Kiosztunk neki egy egyedi ID-t
]]
addEventHandler("onPlayerJoin", root,
function()
	movePlayerToLobby(source)
	assignPlayerID(source)
end)

--[[
	Játékos kilépés kezelése

	Amikor egy játékos kilép a szerverről:
	- Eltávolítjuk az aktuális arenából
	- Felszabadítjuk az ID-ját
]]
addEventHandler("onPlayerQuit", root,
function()
	local arena = getElementData(source, "arena")
	if arena then
		removePlayerFromArena(source, arena)
		movePlayerToLobby(source)
	end
	deassignPlayerID(source)
end)

--[[
	ARENA CSATLAKOZÁS KÉRELEM KEZELÉSE

	Ez az event akkor aktiválódik, amikor egy játékos arenába szeretne csatlakozni.
	A kliens oldal küldi ezt az eventet, amikor a játékos rákattint egy arenára.
]]
addEvent("core:onPlayerRequestJoinArena", true)
addEventHandler("core:onPlayerRequestJoinArena", root,
function(id)
	if type(id) == "string" then
		-- Először eltávolítjuk a jelenlegi arenából
		local currentArena = getElementData(source, "arena")
		-- Ellenőrizzük, hogy már ebben az arenában van-e
		if currentArena == id then
			triggerClientEvent(source, "notification:create", resourceRoot, "Arena", "Már ebben az arenában vagy: "..core.arenas[currentArena].name)
			return
		end
		-- Ha másik arenában van, eltávolítjuk onnan
		if currentArena then
			removePlayerFromArena(source, currentArena)
		end

		local arena = core.arenas[id]
		-- Ellenőrizzük az arena érvényességét és elérhetőségét
		if not arena or not arena.resource or getResourceState(arena.resource) ~= "running" then
			-- Lobbiba küldés, hogy ne ragadjon be
			triggerClientEvent(source, "core:handleClientLeaveArena", resourceRoot)
			movePlayerToLobby(source)
			triggerClientEvent(source, "notification:create", resourceRoot, "Arena", "Ez az arena jelenleg nem elérhető")
			return
		end

		-- Bejelentkezés követelmény ellenőrzése
		if arena.requireLogin and not getElementData(source, "LoggedIn") then
			removePlayerFromArena(source, arena.id)
			movePlayerToLobby(source)
			return triggerClientEvent(source, "notification:create", resourceRoot, "Arena", "Be kell jelentkezned a(z) "..arena.name.." arenához")
		end

		-- Zárolt arena ellenőrzése (csak adminok léphetnek be)
		if arena.locked and not hasObjectPermissionTo(source, "function.kickPlayer", true) then
			removePlayerFromArena(source, arena.id)
			movePlayerToLobby(source)
			return triggerClientEvent(source, "notification:create", resourceRoot, "Arena", "Ez az arena zárolt")
		end

		-- Játékosszám limit ellenőrzése
		local playersCount, maximumPlayers = #getElementChildren(arena.element, "player"), getElementData(arena.element, "maximumPlayers") or 0
		if maximumPlayers > 0 and playersCount >= maximumPlayers and not hasObjectPermissionTo(source, "function.kickPlayer", true) then
			return triggerClientEvent(source, "notification:create", resourceRoot, "Arena", "Ez az arena megtelt, próbáld újra később")
		end

		-- Minden ellenőrzés sikeres - áthelyezés a cél arenába
		movePlayerToArena(source, id)
	end
end)

--[[
	ARENA ELHAGYÁS KÉRELEM KEZELÉSE

	Ez az event akkor aktiválódik, amikor egy játékos el szeretné hagyni az arenát.
	A kliens oldal küldi ezt az eventet, amikor a játékos a "Leave Arena" gombra kattint.
]]
addEvent("core:onPlayerRequestLeaveArena", true)
addEventHandler("core:onPlayerRequestLeaveArena", root,
function()
	local arena = getElementData(source, "arena")
	if not arena then
		return
	end
	removePlayerFromArena(source, arena)
	-- Játékos visszaküldése a lobbiba
	movePlayerToLobby(source)
end)

--[[
	ARENA ÁLLAPOT VÁLTOZÁS KEZELÉSE

	Ez az event akkor aktiválódik, amikor egy arena állapota változik
	(pl. waiting -> running -> finished). Az arena resource küldi ezt az eventet.
]]
addEvent("onArenaStateChanging", true)
addEventHandler("onArenaStateChanging", root,
function(oldState, newState)
	-- Arena új állapotának mentése
	setElementData(source, "state", newState)
end)

--[[
	ARENA MAP INDÍTÁS KEZELÉSE

	Ez az event akkor aktiválódik, amikor egy új map indul az arenában.
	A mapmanager küldi ezt az eventet a map információkkal.
]]
addEvent("onArenaMapStarting", true)
addEventHandler("onArenaMapStarting", root,
function(mapInfo)
	-- Map információk mentése az arena element data-jába
	setElementData(source, "map", mapInfo.mapName)
	setElementData(source, "mapResourceName", mapInfo.resourceName)
end)

--[[
	MANUÁLIS SPECTATE PARANCS

	Ez a parancs lehetővé teszi a játékosoknak, hogy manuálisan spectate módba váltson
	race arenákban. A játékos járműve befagy, és spectate módba kerül.
]]
addCommandHandler("manualspectate",
function(player, command)
	local arena = core.arenas[tostring(getElementData(player, "arena"))]
	if not arena then
		return
	end
	-- Csak race módban működik
	if arena.gamemode ~= "race" then
		return
	end

	local state = getElementData(arena.element, "state")
	local vehicle = getPedOccupiedVehicle(player)

	-- Ha már spectate módban van, kikapcsoljuk
	if getElementData(player, "state") == "spectating" and getElementData(player, "spectating") then
		triggerClientEvent(player, "spectate:stop", resourceRoot, true)
		setElementData(player, "spectating", nil, false)
		return
	end

	local arenaAllowsSpectate = arena.allowSpectating
	-- Ellenőrizzük a jogosultságokat és az arena állapotát
	if (not hasObjectPermissionTo(player, "function.kickPlayer", true) and not arenaAllowsSpectate) or state ~= "running" then
		return
	end

	-- Spectate mód aktiválása, ha a játékos él és járműben van
	if getElementData(player, "state") == "alive" and isElement(vehicle) and not isElementFrozen(vehicle) then
		triggerClientEvent(player, "spectate:start", resourceRoot, true)
		setElementData(player, "state", "spectating")
		setElementData(player, "spectating", true, false)
		-- Jármű befagyasztása
		setElementFrozen(vehicle, true)
	end
end)

--[[
	SPECTATE FELOLDÁS KEZELÉSE

	Ez az event akkor aktiválódik, amikor a játékos kilép a spectate módból.
	A kliens oldal küldi ezt az eventet.
]]
addEvent("spectate:unfreeze", true)
addEventHandler("spectate:unfreeze", root,
function()
	local vehicle = getPedOccupiedVehicle(source)
	if isElement(vehicle) then
		-- Jármű feloldása
		setElementFrozen(vehicle, false)
	end
	-- Játékos állapotának visszaállítása
	setElementData(source, "state", "alive")
end)

--[[
	MAP KEZELÉSI RENDSZER

	Ez a rész a map kompatibilitás ellenőrzését és kezelését végzi.
	Minden játékmód használhatja ezeket a függvényeket a megfelelő mapok kereséséhez.
]]

--[[
	Játékmóddal kompatibilis mapok keresése

	@param string|table gamemodeFilter - Játékmód szűrő (pl. "dm", "race", vagy {"dm", "tdm"})
	@return table, table - Map resource nevek és map nevek

	Ez a függvény megkeresi az összes olyan mapot, amely kompatibilis
	a megadott játékmóddal. A map nevében kell szerepelnie a játékmód nevének.
]]
function getMapsCompatibleWithGamemode(gamemodeFilter)
	if type(gamemodeFilter) ~= "string" and type(gamemodeFilter) ~= "table" then
		return {}, {}
	end
	local maps = {}
	local mapNames = {}

	-- Végigmegyünk az összes resource-on
	for i, resource in pairs(getResources()) do
		local resourceType = getResourceInfo(resource, "type")
		local resourceGamemodes = getResourceInfo(resource, "gamemodes")

		-- Csak a race típusú mapokat vizsgáljuk (MTA standard)
		if resourceType == "map" and resourceGamemodes == "race" then
			local resourceName = getResourceName(resource)
			local mapName = getResourceInfo(resource, "name")

			-- Ellenőrizzük a kompatibilitást
			if resourceName and mapName and isMapCompatibleWithGamemode(mapName, gamemodeFilter) then
				table.insert(maps, resourceName)
				mapNames[resourceName] = mapName
			end
		end
	end
	return maps, mapNames
end

--[[
	Map kompatibilitás ellenőrzése

	@param string mapName - A map neve
	@param string|table gamemodeFilter - Játékmód szűrő
	@return boolean - Kompatibilis-e a map

	Ez a függvény ellenőrzi, hogy egy map kompatibilis-e a megadott játékmóddal.
	A map nevében kell szerepelnie a játékmód nevének (pl. "dm_arena1", "race_track").
]]
function isMapCompatibleWithGamemode(mapName, gamemodeFilter)
	if type(mapName) == "string" then
		if type(gamemodeFilter) == "table" then
			-- Több játékmód esetén mindegyiket ellenőrizzük
			for i, gamemode in pairs(gamemodeFilter) do
				if string.find(mapName:lower(), gamemode:lower(), 1, true) then
					return true
				end
			end
		elseif type(gamemodeFilter) == "string" and string.find(mapName:lower(), gamemodeFilter:lower(), 1, true) then
			return true
		end
	end
	return false
end

--[[
	Mapok frissítése

	@param element arena - Az arena element (opcionális)

	Ez a függvény frissíti a mapok listáját és értesíti a mapmanager-t.
	Új mapok hozzáadása után kell meghívni.
]]
function refreshMaps(arena)
	refreshResources()
	triggerEvent("mapmanager:onMapsRefresh", isElement(arena) and arena or root)
	outputDebugString("Mapok frissítve", 0, 25, 132, 109)
end

--[[
	REFRESHMAPS PARANCS

	Admin parancs a mapok frissítéséhez. Használat:
	/refreshmaps - aktuális arena mapjainak frissítése
	/refreshmaps all - összes arena mapjainak frissítése
]]
addCommandHandler("refreshmaps",
function(player, command, refreshAll)
	if hasObjectPermissionTo(player, "function.kickPlayer", true) then
		outputDebugString(getPlayerName(player):gsub("#%x%x%x%x%x%x", "").." frissíti a mapokat: "..(refreshAll == "all" and "összes arena" or getElementData(player, "arena")), 0, 25, 132, 109)
		refreshMaps(refreshAll == "all" and root or getElementParent(player))
	end
end)

--[[
	Latent eventek törlése játékoshoz

	@param element player - A játékos element

	Ez a függvény törli az összes függőben lévő latent eventet egy játékoshoz.
	Főleg arena váltáskor használatos, hogy ne maradjanak függőben file letöltések.
]]
function cancelLatentsForPlayer(player)
	if isElement(player) then
		local eventHandles = getLatentEventHandles(player)
		if eventHandles then
			for i = 1, #eventHandles do
				cancelLatentEvent(player, eventHandles[i])
			end
		end
	end
end

--[[
	DIMENZIÓ FRISSÍTÉSEK

	Ezek az event handlerek biztosítják, hogy a játékosok és járművek
	mindig a megfelelő dimenzióban legyenek az arenájuknak megfelelően.
]]

--[[
	Játékos spawn dimenzió beállítása

	Amikor egy játékos spawn-ol (újjáéled), biztosítjuk, hogy
	a megfelelő dimenzióban legyen az arenájának megfelelően.
]]
addEventHandler("onPlayerSpawn", root,
function()
	local arena = core.arenas[tostring(getElementData(source, "arena"))]
	if arena then
		setElementDimension(source, arena.dimension)
	end
end)

--[[
	Jármű belépés dimenzió beállítása

	Amikor egy játékos belép egy járműbe (sofőr pozícióba),
	biztosítjuk, hogy mind a játékos, mind a jármű a megfelelő
	dimenzióban legyen. Emellett beállítjuk a rendszámtáblát is.
]]
addEventHandler("onVehicleEnter", root,
function(player, seat)
	if seat == 0 then -- Csak sofőr pozíció esetén
		local arena = core.arenas[tostring(getElementData(player, "arena"))]
		if arena then
			-- Játékos és jármű dimenzió beállítása
			setElementDimension(player, arena.dimension)
			setElementDimension(source, arena.dimension)
		end
		-- Rendszámtábla beállítása a játékos nevére (színkódok nélkül)
		setVehiclePlateText(source, getPlayerName(player):gsub("#%x%x%x%x%x%x", ""))
	end
end)