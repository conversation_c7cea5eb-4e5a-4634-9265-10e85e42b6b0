--[[
    Vultaic MGM - sync_client.l<PERSON>tes magyar kommentek minden sorhoz
--]]

-- <PERSON><PERSON><PERSON> t<PERSON> tábla az elementekhez tartozó aréna adatoknak
local data = {}

--[[
    Beállít egy aréna adatot egy adott elemhez (pl. játékos<PERSON>z) kliens old<PERSON>,
    majd szinkronizálja azt a szerverrel.
    @param element: az MTA element (pl. játékos, aréna)
    @param key: az adat kulcsa
    @param value: az adat értéke
]]
function setArenaData(element, key, value)
    -- Ha még nincs adat az adott elemhez, inicializáljuk
    if not data[element] then
        data[element] = {}
    end
    -- Ha az új érték megegyezik a régivel, nincs teend<PERSON>
    if getElementData(element, key) == value then
        return false
    end
    -- Megjelöljük, hogy ehhez az elemhez tartozik ilyen adat
    data[element][key] = true
    -- Beállítjuk az elementData-t (he<PERSON><PERSON>)
    setElementData(element, tostring(key), value, false)
    -- Átalakítjuk a kulcsot/értéket, ha szükséges (pl. integer/string konverzió)
    local key, value = transformData(key, value)
    -- Esemény a szerver felé: az aréna adat megváltozott
    triggerServerEvent("onClientChangeArenaData", localPlayer, element, key, value)
end

--[[
    Lekér egy aréna adatot egy elemhez (kliens oldali wrapper)
    @param element: az MTA element
    @param key: az adat kulcsa
    @return: az adott kulcs értéke
]]
function getArenaData(element, key)
    return getElementData(element, key)
end

-- Esemény regisztráció: aréna adat változott (szerverről jövő broadcast)
addEvent("onArenaDataChanged", true)
addEventHandler("onArenaDataChanged", root,
    function(key, value)
        -- Ha a kulcs nincs megadva, minden adatot törlünk az adott elemhez
        if not key then
            if isElement(source) and data[source] then
                for key, value in pairs(data[source]) do
                    setElementData(source, tostring(key), nil, false)
                end
            end
        else
            -- Ha van kulcs, átalakítjuk, ha szükséges
            local key, value = replaceDataIDs(key, value)
            -- Ha még nincs adat az elemhez, inicializáljuk
            if not data[source] then
                data[source] = {}
            end
            -- Megjelöljük, hogy ehhez az elemhez tartozik ilyen adat
            data[source][key] = true
            -- Beállítjuk az elementData-t (helyi változás)
            setElementData(source, tostring(key), value, false)
            -- Kliens oldali esemény, hogy az aréna adat megváltozott
            triggerEvent("onClientArenaDataChanged", source, key, value)
        end
    end, true, "high"
)

-- Esemény regisztráció: szerverről érkezik egy teljes aréna adatcsomag
addEvent("receiveArenaData", true)
addEventHandler("receiveArenaData", root,
    function(newData)
        -- Először minden régi adatot törlünk
        for element, elementData in pairs(data) do
            if isElement(element) then
                for key, value in pairs(elementData) do
                    setElementData(element, tostring(key), nil, false)
                end
            end
        end
        -- Lecseréljük a teljes adatstruktúrát az újra
        data = newData
        -- Végigmegyünk az új adatokon, és beállítjuk őket
        for element, elementData in pairs(data) do
            if isElement(element) then
                for key, value in pairs(elementData) do
                    local key, value = replaceDataIDs(key, value)
                    setElementData(element, tostring(key), value, false)
                end
            end
        end
    end, true, "high"
)