--[[
	VULTAIC MGM - MAPTEST ADDON

	Ez a fájl a maptest (map tesztelés) addon logikáját tartalmazza.

	Főbb funkciók:
	- Map elfogadás kezelése
	- Játékosszám alapú korlátozás
	- Map lista frissítése
	- Chat értesítések

	Beállítások:
	- allow_deletes: mely gamemode-okban engedélyezett a törlés

	Működés:
	1. Map elfogadás event fogadása
	2. Játékosszám ellenőrzése (< 100)
	3. Chat üzenet küldése
	4. Map lista frissítése
]]

-- Maptest beállítások
local settings = {
	allow_deletes = {
		race = true,                                        -- Race gamemode-ban engedélyezett a törlés
	},
}

--[[
	Map elfogadás kezelése

	@param string mapName - Elfogadott map neve

	Ez a függvény fut le, amikor egy map elfogadásra kerül:
	1. <PERSON><PERSON><PERSON><PERSON> a já<PERSON>kosszámot (< 100)
	2. Chat üzenetet küld minden játékosnak
	3. <PERSON><PERSON><PERSON><PERSON> a map listát
]]
function onMapAccepted(mapName)
	-- J<PERSON><PERSON>kosszám ellenőrzése (csak kevés játékos esetén)
	if getPlayerCount() < 100 then
		-- Elfogadás üzenet küldése minden játékosnak
		outputChatBox("#19846dMap Testing :: #ffffffThe map #19846d"..mapName.."#ffffff has been #00ff00accepted#ffffff!", root, 255, 255, 255, true)

		-- Map lista frissítése a core rendszerben
		exports.core:refreshMaps()
	end
end