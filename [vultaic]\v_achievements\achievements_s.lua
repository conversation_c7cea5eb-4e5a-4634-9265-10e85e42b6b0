--[[
	VULTAIC MGM - ACHIEVEMENT (TELJESÍTMÉNY) RENDSZER SZERVER OLDAL

	Ez a fájl az achievement (teljesítmény) rendszer szerver oldali logikáját tartalmazza.

	Főbb funkciók:
	- Achievement definíciók kezelése
	- Játékos teljesítmények nyilvántartása
	- Automatikus achievement feloldás
	- Jutalmak kiosztása
	- Adatbázis integráció

	Achievement típusok:
	- P<PERSON>z alapú (money)
	- Toptimes alapú (legjobb idők)
	- Arena pontok alapú (dm_points, os_points, dd_points, race_points, shooter_points, hunter_points)

	FONTOS MEGJEGYZÉS:
	Az achievement tábla indexeinek sorrendje SOHA nem változhat!
	Ha például a [0] index "toptimes"-ra változna, akkor azok a játékosok,
	akik az 1M$ achievement-et feloldották, helyette a toptimes-t kap<PERSON>k.
	Új achievement-ek mindig a tábla vé<PERSON><PERSON> ker<PERSON>!
]]

-- Fejlesztői mód beállítása
local DEVELOPMENT_MODE = true

-- Adatbázis név meghatározása port alapján
local DB_NAME = (getServerPort() ~= 22003 and DEVELOPMENT_MODE) and "v_accounts_dev" or "v_accounts"

-- Globális változók
g_Connection = nil          -- MySQL kapcsolat objektum

--[[
	ACHIEVEMENT DEFINÍCIÓK

	Minden achievement a következő formátumban van definiálva:
	{stat_key, név, szükséges_érték, leírás, jutalom_pénz}

	FIGYELEM: A sorrend megváltoztatása TILOS!
]]
g_Achievements = {
	-- Pénz alapú achievement
	{"money", "Money? I got that!", 1000000, "Get 1 million in cash", 0},

	-- Toptimes alapú achievement
	{"toptimes", "Remember the name!", 1000, "Get 1K toptimes", 25000},

	-- Deathmatch arena achievementek
	{"dm_points", "Deathmatch Rookie!", 10000, "Get 10K points in Deathmatch arena", 50000},
	{"dm_points", "King of Deathmatch!", 100000, "Get 100K points in Deathmatch arena", 75000},

	-- Oldschool arena achievementek
	{"os_points", "Old, but gold!", 10000, "Get 10K points in Oldschool arena", 50000},
	{"os_points", "King of Oldschool!", 100000, "Get 100K points in Oldschool arena", 75000},

	-- Destruction Derby arena achievementek
	{"dd_points", "They know me for my destruction!", 10000, "Get 10K points in DD arena", 50000},
	{"dd_points", "King of Destruction Derby!", 100000, "Get 100K points in DD arena", 75000},

	-- Race arena achievementek
	{"race_points", "Step up to my wheels!", 10000, "Get 10K points in Race arena", 50000},
	{"race_points", "King of Race!", 100000, "Get 100K points in Race arena", 75000},

	-- Shooter arena achievementek
	{"shooter_points", "Shoot or get shot!", 10000, "Get 10K points in Shooter arena", 50000},
	{"shooter_points", "King of Shooter!", 100000, "Get 100K points in Shooter arena", 75000},

	-- Hunter arena achievementek
	{"hunter_points", "Don't flame, get Aim!", 10000, "Get 10K points in Hunter arena", 50000},
	{"hunter_points", "King of Hunter!", 100000, "Get 100K points in Hunter arena", 75000},
}

-- Játékos achievementek cache tárolása
g_PlayerAchievements = {}
function onScriptLoad()
	g_Connection, error = dbConnect("mysql", "dbname="..DB_NAME..";host=127.0.0.1;port=3306", "sunuser", "SunUser2025@&#")
	if g_Connection then
		dbExec(g_Connection, "CREATE TABLE IF NOT EXISTS v_achievements (`account_id` INT NOT NULL, `achievement_id` INT NOT NULL)")
		for _, player in ipairs(getElementsByType("player")) do
			if getElementData(player, "LoggedIn") then
				CAchievement:new(player)
			end
		end
		outputDebugString("Achievements MySQL: Connected")
	else
		outputDebugString("Achievements MySQL: Failed to connect")
	end
end
addEventHandler("onResourceStart", resourceRoot, onScriptLoad)

function onPlayerLogin(userdata)
	CAchievement:new(source)
end
addEvent("mysql:onPlayerLogin", true)
addEventHandler("mysql:onPlayerLogin", root, onPlayerLogin)

function onPlayerStatsUpdate(key, newValue)
	CAchievement:onPlayerStatsUpdate(source, key, newValue)
end
addEvent("CAccount:onPlayerStatsUpdate", true)
addEventHandler("CAccount:onPlayerStatsUpdate", root, onPlayerStatsUpdate)

function onPlayerRequestList()
	triggerClientEvent(client, "Achievements:onPlayerReceiveList", client, g_Achievements, g_PlayerAchievements[client])
end
addEvent("Achievements:onPlayerRequestList", true)
addEventHandler("Achievements:onPlayerRequestList", resourceRoot, onPlayerRequestList)