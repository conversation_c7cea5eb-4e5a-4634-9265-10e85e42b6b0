--[[
	VULTAIC MGM - RACE JÁTÉKMÓD KLIENS OLDAL

	Ez a fájl a race játékmód kliens oldali logikáját tartalmazza:
	- Arena csatlakozás/elhagyás kezelése
	- Spectate rendszer
	- UI és HUD kezelés
	- Billentyű kötések
	- Játékos állapot kezelés
	- Checkpoint és befejezés kezelés
	- Víz alatti ellenőrzés
]]

-- Arena kliens objektum
arena = {
	readyTimer = Timer:create(),        -- K<PERSON><PERSON> jelz<PERSON> timer
	waterCheckTimer = Timer:create()    -- V<PERSON>z alatti ellenőrz<PERSON> timer
}

-- Vízi járművek ID listája - ezek víz alatt is működhetnek
local waterCraftIDS = {
	[539] = true,  -- Vortex (hovercraft)
	[460] = true,  -- Skimmer (repülő)
	[417] = true,  -- <PERSON><PERSON><PERSON> (helikopter)
	[447] = true,  -- <PERSON><PERSON><PERSON> (helikopter)
	[472] = true,  -- Coastguard (csónak)
	[473] = true,  -- Dinghy (csónak)
	[493] = true,  -- Jetmax (csónak)
	[595] = true,  -- Launch (csónak)
	[484] = true,  -- Marquis (yacht)
	[430] = true,  -- Predator (csónak)
	[453] = true,  -- Reefer (csónak)
	[452] = true,  -- Speeder (csónak)
	[446] = true,  -- Squalo (csónak)
	[454] = true   -- Tropic (csónak)
}

-- Spectate rendszer objektum
spectate = {}

-- Enter billentyű lekérdezése (általában Enter vagy F)
enterKey = next(getBoundKeys("enter_exit"))

--[[
	EVENT DEFINÍCIÓK

	Ezek az eventek biztosítják a kommunikációt a szerver és kliens között.
]]
addEvent("onClientJoinArena", true)              -- Arena csatlakozás
addEvent("onClientLeaveArena", true)             -- Arena elhagyás
addEvent("onClientPlayerJoinArena", true)        -- Másik játékos csatlakozás
addEvent("onClientPlayerLeaveArena", true)       -- Másik játékos elhagyás
addEvent("onClientArenaStateChanging", true)     -- Arena állapot változás
addEvent("onClientArenaMapStarting", true)       -- Map indítás
addEvent("onClientArenaSpawn", true)             -- Játékos spawn
addEvent("onClientArenaWasted", true)            -- Játékos halál
addEvent("onClientArenaFinished", true)          -- Verseny befejezés
addEvent("checkpoints:onClientFinish", true)     -- Checkpoint befejezés
addEvent("onClientArenaGridCountdown", true)     -- Grid countdown
addEvent("onClientArenaNextmapChanged", true)    -- Következő map változás
addEvent("onClientArenaRequestSpectateStart", true)  -- Spectate indítás kérés
addEvent("onClientArenaRequestSpectateStop", true)   -- Spectate leállítás kérés
addEvent("onClientArenaRequestSpectateEnd", true)    -- Spectate befejezés kérés
addEvent("onClientArenaPlayerStateChange", true)     -- Játékos állapot változás
addEvent("mapmanager:onMapLoad", true)           -- Map betöltés

--[[
	EVENT KEZELŐK - ARENA CSATLAKOZÁS ÉS ELHAGYÁS
]]

--[[
	Arena csatlakozás kezelése

	@param table data - Arena adatok a szerverről

	Ez az event akkor aktiválódik, amikor a játékos csatlakozik a race arenához.
	Beállítja a szükséges kezelőket, billentyű kötéseket és UI elemeket.
]]
addEventHandler("onClientJoinArena", resourceRoot,
function(data)
	-- Arena adatok szinkronizálása
	for i, v in pairs(data) do
		arena[i] = v
	end

	-- Játékmenet beállítások
	setBlurLevel(0)                                    -- Blur effekt kikapcsolása
	setPedCanBeKnockedOffBike(localPlayer, false)      -- Motorról leesés tiltása

	-- Parancs kezelők regisztrálása
	addCommandHandler("suicide", requestKill)          -- /suicide parancs
	addCommandHandler("kill", requestKill)             -- /kill parancs
	addEventHandler("mapmanager:onMapLoad", localPlayer, requestToptimes)
	addEventHandler("checkpoints:onClientFinish", root, handleRaceFinish)
	addEventHandler("onClientPreRender", root, cancelCameraDrop)

	-- Billentyű kötések
	bindKey(enterKey, "down", "suicide")               -- Enter = öngyilkosság
	bindKey("b", "down", "manualspectate")             -- B = manuális spectate

	-- UI elemek megjelenítése
	triggerEvent("radar:setVisible", localPlayer, true, true)  -- Radar megjelenítése
	triggerEvent("racehud:show", localPlayer, {                -- Race HUD megjelenítése
		state = data.state,
		mapName = data.mapInfo.mapName,
		nextMapName = data.nextMap,
		timeIsUpStartTick = getTickCount(),
		timeIsUpDuration = data.duration
	})
end)

addEventHandler("onClientLeaveArena", resourceRoot,
function()
	spectate.stop()
	-- Reset gameplay functions
	setPedCanBeKnockedOffBike(localPlayer, true)
	arena.waterCheckTimer:killTimer()
	arena.readyTimer:killTimer()
	gridCountdown(false)
	-- Handlers
	removeCommandHandler("suicide", requestKill)
	removeCommandHandler("kill", requestKill)
	removeEventHandler("mapmanager:onMapLoad", localPlayer, requestToptimes)
	removeEventHandler("checkpoints:onClientFinish", root, handleRaceFinish)
	removeEventHandler("onClientPreRender", root, cancelCameraDrop)
	-- Binds
	unbindKey(enterKey, "down", "suicide")
	unbindKey("b", "down", "manualspectate")
	-- UI stuff
	triggerEvent("radar:setVisible", localPlayer, false)
	triggerEvent("racehud:hide", localPlayer)
	arena.timeIsUpStartTick = nil
	arena.timeIsUpTick = nil
end)

addEventHandler("onClientArenaStateChanging", resourceRoot,
function(currentState, newState, data)
	arena.state = newState and newState or arena.state
	if arena.state == "running" then
		arena.timeIsUpStartTick = getTickCount()
		arena.timeIsUpTick = arena.timeIsUpStartTick + data.duration
	end
end)

addEventHandler("onClientArenaMapStarting", resourceRoot,
function(mapInfo)
	arena.mapInfo = mapInfo
end)

function gridCountdown(countdown)
	if countdown == 0 then
		triggerEvent("racepickups:checkAllPickups", localPlayer)
		local vehicle = arena.vehicle or getPedOccupiedVehicle(localPlayer)
		if isElement(vehicle) then
			setElementFrozen(localPlayer, false)
			setElementFrozen(vehicle, false)
			setVehicleDamageProof(vehicle, false)
			setElementCollisionsEnabled(vehicle, true)
		end
	end
end
addEventHandler("onClientArenaGridCountdown", resourceRoot, gridCountdown)

addEventHandler("onClientArenaNextmapChanged", resourceRoot,
function(nextMap)
	arena.nextMap = nextMap or nil
end)

addEventHandler("onClientArenaSpawn", resourceRoot,
function(vehicle)
	arena.vehicle = vehicle
	spectate.stop()
	setCameraTarget(localPlayer)
	arena.readyTimer:setTimer(function()
		local cameraTarget = getCameraTarget()
		if isElement(cameraTarget) and cameraTarget == localPlayer and isPedInVehicle(localPlayer) then
			triggerServerEvent("onPlayerReady", localPlayer)
			removeVehicleNitro()
			fadeCamera(true)
			arena.readyTimer:killTimer()
		end
	end, 500, 0)
	arena.waterCheckTimer:setTimer(checkWater, 1000, 0)
	removeVehicleNitro()
	fixVehicle(arena.vehicle)
	toggleAllControls(true)
	triggerEvent("racepickups:updateVehicleWeapons", localPlayer)
end)

addEventHandler("onClientArenaWasted", resourceRoot,
function()
	spectate.start()
	arena.readyTimer:killTimer()
end)

addEventHandler("onClientArenaFinished", resourceRoot,
function()
	spectate.start()
	arena.readyTimer:killTimer()
	arena.waterCheckTimer:killTimer()
end)

function handleRaceFinish()
	setCameraMatrix(getCameraMatrix())
	toggleAllControls(false, true, false)
	local timePassed = getTimePassed()
	if timePassed then
		triggerServerEvent("toptimes:onPlayerRequestAddToptime", localPlayer, timePassed)
	end
end

function spectate.start(...)
	exports.core:startSpectating(...)
end

addEventHandler("onClientArenaRequestSpectateStart", resourceRoot,
function()
	spectate.start(true)
end)

function spectate.stop(...)
	exports.core:stopSpectating(...)
end
addEventHandler("onClientArenaRequestSpectateStop", resourceRoot, spectate.stop)

addEventHandler("onClientArenaRequestSpectateEnd", resourceRoot,
function()
	exports.core:forcedStopSpectating()
end)

addEventHandler("onClientArenaPlayerStateChange", resourceRoot,
function(player, state)
	if player ~= localPlayer then
		if state == "alive" then
			setPlayerVisible(player, true)
		else
			setPlayerVisible(player, false)
		end
	end
end)

addEventHandler("onClientPlayerJoinArena", resourceRoot,
function(player)
	tableInsert(arena.players, player)
	if player == localPlayer then
		return
	end
	setPlayerVisible(player, false)
end)

addEventHandler("onClientPlayerLeaveArena", resourceRoot,
function(player)
	tableRemove(arena.players, player)
end)

function requestKill()
	if arena.state == "running" and not isPlayerDead(localPlayer) then
		triggerServerEvent("onRequestKillPlayer", localPlayer)
	end
end

function checkWater()
	if isElement(arena.vehicle) then
		if not waterCraftIDS[getElementModel(arena.vehicle)] then
			local x, y, z = getElementPosition(localPlayer)
			local waterZ = getWaterLevel(x, y, z)
			if waterZ and z < waterZ - 0.5 and not isPlayerDead(localPlayer) then
				if (isPlayerDead(localPlayer) and not training.stats.active) then
					return
				end
				setElementHealth(localPlayer, 0)
				triggerServerEvent("onRequestKillPlayer", localPlayer)
			end
		end
		if not getVehicleEngineState(arena.vehicle) then
			setVehicleEngineState(arena.vehicle, true)
		end
	end
end

function removeVehicleNitro()
	if isElement(arena.vehicle) then
		removeVehicleUpgrade(arena.vehicle, 1010)
	end
end

function setPlayerVisible(player, visible)
	if isElement(player) then
		local vehicle = getPedOccupiedVehicle(player)
		local visibleDimension = getElementDimension(localPlayer)
		if visible then
			setElementDimension(player, visibleDimension)
			if isElement(vehicle) then
				setElementDimension(vehicle, visibleDimension)
			end
		else
			visibleDimension = visibleDimension + 1
			setElementDimension(player, visibleDimension)
			if isElement(vehicle) then
				setElementDimension(vehicle, visibleDimension)
			end
		end
	end
end

function isPlayerDead(player)
	return not getElementHealth(player) or getElementHealth(player) < 1e-45 or isPedDead(player)
end

function getTimePassed()
	if arena.timeIsUpStartTick and arena.timeIsUpTick then
		return math.max(getTickCount() - arena.timeIsUpStartTick, 0)
	end
	return nil
end

function getTimeLeft()
	if arena.timeIsUpStartTick and arena.timeIsUpTick then
		return math.max(arena.timeIsUpTick - getTickCount(), 0)
	end
	return nil
end

function requestToptimes()
	triggerServerEvent("toptimes:onPlayerRequestToptimes", localPlayer)
end

function cancelCameraDrop()
	if isPlayerDead(localPlayer) then
		setCameraMatrix(getCameraMatrix())
	end
end

_getCameraTarget = getCameraTarget
function getCameraTarget()
	local target = _getCameraTarget()
	if isElement(target) and getElementType(target) == "vehicle" then
		target = getVehicleOccupant(target)
	end
	return target
end