--[[
	VULTAIC MGM - SMOKE RENDSZER KLIENS OLDAL

	Ez a fájl a smoke (füst) rendszer kliens oldali logikáját tartalmazza.

	Főbb funkciók:
	- Füst effektek letiltása/engedélyezése
	- Shader alapú füst eltávolítás
	- Beállítások integráció
	- Értesítések
	- Performance optimalizáció

	Shader működés:
	- nosmoke.fx shader alkalmazása
	- collisionsmoke texture cseréje
	- cloudmasked texture cseréje
	- Átlátszó blink texture használata

	Működés:
	1. Beállítások ellenőrzése (disable_smoke)
	2. Shader alkalmazása/eltávolítása
	3. World texture-ök módosítása
	4. Értesítés küldése a változásról
]]

-- Shader és texture inicializálás
local noSmokeShader = dxCreateShader("fx/nosmoke.fx")       -- Füst eltávolító shader
local blink = dxCreateTexture("fx/blink.png")               -- Átlátszó replacement texture
dxSetShaderValue(noSmokeShader, "gTexture", blink)         -- Shader texture beállítása
local smokeDisabled = false                                 -- Füst letiltás állapota

--[[
	Füst letiltása

	Shader alkalmazása a world texture-ökre a füst eltávolításához.
]]
function disableSmoke()
	engineApplyShaderToWorldTexture(noSmokeShader, "collisionsmoke")  -- Ütközési füst
	engineApplyShaderToWorldTexture(noSmokeShader, "cloudmasked")     -- Felhő füst
end

--[[
	Füst engedélyezése

	Shader eltávolítása a world texture-ökről a füst visszaállításához.
]]
function enableSmoke()
	engineRemoveShaderFromWorldTexture(noSmokeShader, "collisionsmoke")  -- Ütközési füst visszaállítása
	engineRemoveShaderFromWorldTexture(noSmokeShader, "cloudmasked")     -- Felhő füst visszaállítása
end

--[[
	RESOURCE INDÍTÁS KEZELÉSE

	Beállítások ellenőrzése és füst állapot inicializálása.
]]
addEventHandler("onClientResourceStart", resourceRoot,
function()
	local state = exports.v_settings:getClientVariable("disable_smoke") == "On"
	if state then
		toggleSmoke(state)
	end
end)

--[[
	BEÁLLÍTÁS VÁLTOZÁS KEZELÉSE

	disable_smoke beállítás változásának figyelése.
]]
addEvent("settings:onSettingChange", true)
addEventHandler("settings:onSettingChange", localPlayer,
function(variable, value)
	if variable == "disable_smoke" then
		local state = value == "On"
		toggleSmoke(state, true)  -- Értesítéssel együtt
	end
end)

--[[
	Füst állapot váltása

	@param boolean state - Füst letiltás állapota
	@param boolean notifyDisplay - Értesítés megjelenítése

	Füst engedélyezése/letiltása shader alkalmazásával.
]]
function toggleSmoke(state, notifyDisplay)
	smokeDisabled = state and true or false
	if smokeDisabled then
		disableSmoke()
		if notifyDisplay then
			triggerEvent("notification:create", localPlayer, "Anti-smoke", "Smokes are now disabled")
		end
	else
		enableSmoke()
		if notifyDisplay then
			triggerEvent("notification:create", localPlayer, "Anti-smoke", "Smokes are now enabled")
		end
	end
end