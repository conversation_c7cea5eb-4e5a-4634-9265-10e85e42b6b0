# Rendszer Biztonsági Dokumentáció – Vultaic-MGM-master

## Tartalomjegyzék
1. [<PERSON><PERSON><PERSON><PERSON><PERSON> biztonsági architektúra](#általános-biztonsági-architektúra)
2. [Szerver-oldali validációk](#szerver-oldali-validációk)
3. [<PERSON><PERSON><PERSON><PERSON> <PERSON> kapcsolat kockázatai és védekezés](#külső-api-kapcsolat-kockázatai-és-védekezés)
4. [Serial, név, duplikált account védelem](#serial-név-duplikált-account-védelem)
5. [Parancs naplózás, chat flood/spam védelem](#parancs-naplózás-chat-floodspam-védelem)
6. [Session és aktív próbálkozások kezelése](#session-és-aktív-próbálkozások-kezelése)
7. [Adatbázis biztonság](#adatbázis-biztonság)
8. [Aj<PERSON><PERSON>t további védelmi intézkedések](#ajánlott-további-védelmi-intézkedések)

---

## Általános biztonsági architektúra
- Minden kritikus adatot szerver oldalon is validál a rendszer.
- Magas modularitás: minden biztonsági funkció külön resource-ban kezelhető (`v_security`, `v_notify`, `v_login`, `v_mysql`).
- Naplózás, monitoring, admin értesítések.
- Fórum alapú hitelesítés, központi user management.

---

## Szerver-oldali validációk
- Név, szín, input hossz, karakterkészlet, típus mindenhol szerver oldalon is ellenőrizve.
- Admin funkciókhoz, parancsokhoz kizárólag szerver oldali jogosultság ellenőrzés.
- Statok, leaderboard adatok minden frissítése előtt szerver oldali validáció.
- Input tisztítás (`sanitizeString`), típusellenőrzés, tartományellenőrzés.

---

## Külső API kapcsolat kockázatai és védekezés

A fórum alapú bejelentkezés külső API-n keresztül valósul meg, ami potenciális támadási felületet jelenthet. A legnagyobb kockázatok: köztes támadás (ha nincs HTTPS), endpoint sérülékenység, adatmanipuláció, brute force próbálkozások, és az API elérhetetlensége.

**A kockázat mértéke:**
- Közepes-magas, ha nincs megfelelő védelem.
- Alacsony-közepes, ha minden kommunikáció titkosított, az API auditált, rate-limittel védett, és a válaszokat szigorúan validálják.

**Védekezési lehetőségek:**
- HTTPS használata mindenhol
- API endpoint audit, SQL injection és brute force elleni védelem
- Rate limiting bevezetése
- Szigorú válasz validáció szerver oldalon
- API kulcs vagy digitális aláírás használata
- Naplózás, monitoring, riasztás gyanús aktivitás esetén
- Graceful degradation/failover, ha az API nem elérhető

---

## Serial, név, duplikált account védelem
- Minden játékos serialját szerver oldalon ellenőrzi a rendszer.
- Egy accounttal egyszerre csak egy játékos léphet be (duplikált login tiltás).
- Név ütközések, tiltott nevek szerver oldali szűrése.

---

## Parancs naplózás, chat flood/spam védelem
- Minden admin és játékos parancs naplózásra kerül.
- Chat flood/spam védelem szerver oldalon (üzenetek gyakoriságának limitálása, mute, kick).
- Külön resource-ok kezelik a chat és parancs naplózást.

---

## Session és aktív próbálkozások kezelése
- Minden bejelentkezési próbálkozás naplózva, aktív session-ök szerver oldalon követve.
- Brute force elleni védelem: limitált számú próbálkozás, időkorlát.
- Session érvényesség, automatikus kiléptetés inaktivitás esetén.

---

## Adatbázis biztonság
- Minden SQL lekérdezés prepared statement-tel történik.
- Jogosultságok szigorú kezelése: az adatbázis felhasználónak csak a szükséges jogok.
- Adatbázis kapcsolat csak localhostról engedélyezett.
- Jelszavak hash-elve, soha nem tárolódnak plain textben.

---

## Ajánlott további védelmi intézkedések
- **API endpointok rendszeres auditja, naplózás ellenőrzése**
- **Külső API esetén digitális aláírás, IP szűrés**
- **Admin parancsokhoz kétfaktoros hitelesítés (2FA)**
- **Automatikus backup, log rotáció, monitoring**
- **Biztonsági frissítések rendszeres telepítése (MTA, Linux, MySQL, PHP)**
- **Felhasználói aktivitás auditálása, gyanús viselkedés detektálása**

---

_Készült: 2025.08.12._
