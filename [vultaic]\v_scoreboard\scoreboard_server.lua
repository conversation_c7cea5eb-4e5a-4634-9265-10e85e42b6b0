--[[
	VULTAIC MGM - SCOREBOARD RENDSZER SZERVER OLDAL

	Ez a fájl a scoreboard (eredménytábla) rendszer szerver oldali logikáját tartalmazza.

	Főbb funkciók:
	- Szerver információk szinkronizálása
	- Játékos országkód detektálás
	- IP alapú geolokáció
	- Szerver név és maximum játékos szám kezelése
	- Automatikus ország detektálás csatlakozáskor

	API integráció:
	- ipinfodb.com API használata
	- JSON válasz feldolgozás
	- Aszinkron HTTP kérések

	Működés:
	1. Sync element létrehozása szerver adatokkal
	2. Játékos csatlakozáskor IP alapú ország detektálás
	3. Eredmény mentése element data-ba
	4. Event triggerelés a detektálás után
]]

-- Szinkronizációs element létrehozása szerver adatokkal
local syncElement = createElement("scoreboard.syncElement", "scoreboard.syncElement")
setElementData(syncElement, "serverName", tostring(getServerName()))        -- Szerver neve
setElementData(syncElement, "maximumPlayers", getMaxPlayers())              -- Maximum játékos szám

--[[
	RESOURCE INDÍTÁS KEZELÉSE

	Meglévő játékosok országkódjának frissítése.
]]
addEventHandler("onResourceStart", resourceRoot,
function()
	for i, player in pairs(getElementsByType("player")) do
		updatePlayerCountry(player)
	end
end)

--[[
	JÁTÉKOS CSATLAKOZÁS KEZELÉSE

	Új játékos országkódjának detektálása.
]]
addEventHandler("onPlayerJoin", root,
function()
	updatePlayerCountry(source)
end)

--[[
	Játékos országkód frissítése

	@param element player - A játékos element

	IP alapú geolokációval meghatározza a játékos országát.
	Aszinkron HTTP kérést küld az ipinfodb.com API-hoz.
]]
function updatePlayerCountry(player)
	if player then
		-- HTTP kérés az ipinfodb.com API-hoz
		fetchRemote("http://api.ipinfodb.com/v3/ip-country/?key=****************************************************************&format=json&ip="..getPlayerIP(player),
			md5(getPlayerName(player)),  -- Queue név (egyedi azonosító)
			5,                           -- Prioritás
			5000,                        -- Timeout (5 másodperc)
		function(responseData, errorNo, player)
			if errorNo == 0 and isElement(player) then
				-- JSON válasz feldolgozása
				local data = fromJSON(responseData) or {}

				-- Országkód és név beállítása
				setElementData(player, "countryCode", data.countryCode or "N/A")
				setElementData(player, "countryName", data.countryName or "N/A")

				-- Event triggerelés a sikeres detektálás után
				triggerEvent("onPlayerCountryDetected", player, data)
			end
		end, "", false, player)
	end
end