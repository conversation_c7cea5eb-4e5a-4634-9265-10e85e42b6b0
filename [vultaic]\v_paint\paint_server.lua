--[[
	VULTAIC MGM - PAINT RENDSZER SZERVER OLDAL

	Ez a fájl a paint (festés/matricák) rendszer szerver oldali logikáját tartalmazza.

	Főbb funkciók:
	- Sticker/matrica vásárlás és kezelés
	- Slot alapú matrica rendszer
	- Don<PERSON> b<PERSON><PERSON> (több slot)
	- Matrica beállítások mentése/betöltése
	- JSON alapú adattárolás
	- MySQL integráció

	Slot rendszer:
	- Alap játékosok: 16 slot
	- Donatorok: 32 slot

	Működés:
	1. Sticker vásárlás pénzért
	2. Slot szám meghatározása donator státusz alapján
	3. <PERSON><PERSON><PERSON> mentése slot_1, slot_2, ... formátumban
	4. JSON szerializáció az adattároláshoz
]]

--[[
	RESOURCE INDÍTÁS KEZELÉSE

	Sticker árak betöltése és meglévő játékosok frissítése.
]]
addEventHandler("onResourceStart", resourceRoot,
function()
	-- Tuning árak lekérése
	local upgradePrices, exclusiveUpgrades = exports.v_tuning:getTuningUpgrades()
	stickersPrice = upgradePrices["Stickers"]

	-- Meglévő játékosok sticker adatainak frissítése
	for i, player in pairs(getElementsByType("player")) do
		updatePlayerStickers(player)
	end
end)

--[[
	Játékos sticker adatainak frissítése

	@param element player - A játékos element

	Betölti és szinkronizálja a játékos sticker beállításait.
	Donator státusz alapján határozza meg a slot számot.
]]
function updatePlayerStickers(player)
	if isElement(player) then
		-- Sticker vásárlás ellenőrzése
		local bought = getPlayerTuningStats(player, "stickers_bought")
		if bought then
			-- Slot szám meghatározása donator státusz alapján
			local premium = isPlayerDonator(player)
			local slots = premium and 32 or 16  -- Donator: 32 slot, Alap: 16 slot

			setElementData(player, "stickers_bought", true)
			setElementData(player, "paint_slots", slots)

			-- Sticker adatok betöltése
			local data = getPlayerTuningStats(player, "stickers")
			if data then
				-- Minden slot feldolgozása
				for i = 1, slots do
					local slot = data["slot_"..i] or nil
					if slot then
						-- Adatok string konverziója
						for k, v in pairs(slot) do
							slot[k] = tostring(v)
						end
					end
					-- Slot adat beállítása JSON formátumban
					setElementData(player, "paint_slot_"..i, slot and toJSON(slot) or nil)
				end
			end
		end
	end
end

-- Event kezelők a bejelentkezéshez
addEvent("mysql:onPlayerLogin", true)
addEventHandler("mysql:onPlayerLogin", root, function() updatePlayerStickers(source) end)
addEventHandler("onPlayerLogin", root, function() updatePlayerStickers(source) end)

function handleStickersPurchase()
	if not getElementData(source, "LoggedIn") then
		return triggerClientEvent(source, "notification:create", source, "Purchase failed", "You are not logged in")
	end
	local bought = getPlayerTuningStats(source, "stickers_bought")
	if bought then
		return triggerClientEvent(source, "notification:create", source, "Purchase failed", "You have already bought stickers")
	end
	local money = tonumber(getElementData(source, "money") or 0)
	if money < stickersPrice then
		return triggerClientEvent(source, "notification:create", source, "Purchase failed", "You don't have enough money to buy stickers")
	end
	local premium = isPlayerDonator(player)
	local slots = premium and 32 or 16
	setPlayerTuningStats(source, "stickers_bought", "1")
	setElementData(source, "stickers_bought", true)
	setElementData(source, "paint_slots", slots)
	takePlayerStats(source, "money", stickersPrice)
	triggerClientEvent(source, "notification:create", source, "Purchase successful", "You have successfully bought stickers")
end
addEvent("purchaseStickers", true)
addEventHandler("purchaseStickers", root, handleStickersPurchase)

function handleStickersUpdate(data)
	if type(data) ~= "table" then
		return print("Failed to update stickers for "..getPlayerName(source))
	end
	local bought = getPlayerTuningStats(source, "stickers_bought")
	if not bought then
		return triggerClientEvent(source, "notification:create", source, "Update failed", "You didn't buy stickers")
	end
	local slots = tonumber(getElementData(source, "paint_slots") or 16)
	if slots then
		for i = 1, slots do
			local slot = data["slot_"..i] or nil
			if slot then
				for k, v in pairs(slot) do
					slot[k] = tostring(v)
				end
			end
			setElementData(source, "paint_slot_"..i, slot and toJSON(slot) or nil)
		end
		setPlayerTuningStats(source, "stickers", data)
		print(getPlayerName(source).." has just updated his stickers")
		triggerClientEvent(source, "notification:create", source, "Updated", "You have successfully updated your stickers")
	end
end
addEvent("updateStickers", true)
addEventHandler("updateStickers", root, handleStickersUpdate)

setPlayerTuningStats = function(player, stats, value, ...)
	return exports.v_mysql:setPlayerTuningStats(player, stats, value, ...)
end

getPlayerTuningStats = function(player, stats, ...)
	return exports.v_mysql:getPlayerTuningStats(player, stats, ...)
end

takePlayerStats = function(player, stats, ...)
	return exports.v_mysql:takePlayerStats(player, stats, ...)
end

function isPlayerDonator(player)
	return exports.v_donatorship:isPlayerDonator(player)
end