# Vultaic MGM - <PERSON>t<PERSON><PERSON><PERSON>s Beállítási Útmutató

## Tartalomjegyzék
1. [<PERSON><PERSON><PERSON><PERSON><PERSON>](#<PERSON><PERSON><PERSON><PERSON><PERSON>)
2. [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> struktúra](#adatbázis-struktúra)
3. [<PERSON><PERSON><PERSON> táblák létrehozása](#főbb-táblák-létrehozása)
4. [Kiegészí<PERSON>ő táblák](#kiegészítő-táblák)
5. [MySQL konfiguráció frissítése](#mysql-konfiguráció-frissítése)
6. [Adatb<PERSON><PERSON>s kapcsolat tesztelése](#adatbázis-kapcsolat-tesztelése)
7. [Biztonsági mentés beállítása](#biztonsági-mentés-beállítása)
8. [Hibaelhárítás](#hibaelhárítás)

---

## Áttekintés

A Vultaic MGM rendszer MySQL/MariaDB adatbázist használ a já<PERSON> adatok, s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, achievementek és egyéb információk tárolására. A rendszer két adatbázist használ:

- **v_accounts** - Éles szerver adatbázis
- **v_accounts_dev** - Fejlesztői/teszt adatbázis

### Adatbázis kapcsolat konfigurációja
A rendszer automatikusan választja ki az adatbázist a szerver port alapján:
- Port 22003: `v_accounts` (éles)
- Egyéb portok: `v_accounts_dev` (fejlesztői)

---

## Adatbázis struktúra

### Kapcsolódási adatok (mysql_server.lua alapján)
```lua
-- Jelenlegi hardkódolt beállítások (BIZTONSÁGI KOCKÁZAT!)
host: 127.0.0.1
port: 3306
user: root
password: M1RAg3_Zz@ST3R

-- Ajánlott beállítások
host: 127.0.0.1
port: 3306
user: mtauser
password: [ERŐS JELSZÓ]
```

### Fő adatbázisok
- **v_accounts** - Játékos fiókok és statisztikák
- **v_accounts_dev** - Fejlesztői környezet
- **mta_toptimes** - Legjobb idők (külön adatbázis)

---

## Főbb táblák létrehozása

### 1. Kapcsolódás MySQL-hez
```bash
mysql -u root -p
# vagy
mysql -u mtauser -p
```

### 2. Adatbázisok létrehozása
```sql
-- Éles adatbázis
CREATE DATABASE IF NOT EXISTS v_accounts 
CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Fejlesztői adatbázis
CREATE DATABASE IF NOT EXISTS v_accounts_dev 
CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Toptimes adatbázis
CREATE DATABASE IF NOT EXISTS mta_toptimes 
CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Felhasználó létrehozása és jogosultságok
CREATE USER IF NOT EXISTS 'sunuser'@'localhost' IDENTIFIED BY 'SunUser2025@&#';
GRANT ALL PRIVILEGES ON v_accounts.* TO 'sunuser'@'localhost';
GRANT ALL PRIVILEGES ON v_accounts_dev.* TO 'sunuser'@'localhost';
GRANT ALL PRIVILEGES ON mta_toptimes.* TO 'sunuser'@'localhost';
FLUSH PRIVILEGES;
```

### 3. Fő játékos tábla (vmtasa_accounts)
```sql
USE v_accounts;

CREATE TABLE IF NOT EXISTS vmtasa_accounts (
    account_id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(32) NOT NULL UNIQUE,
    password_hash VARCHAR(128) NOT NULL,
    email VARCHAR(100),
    serial VARCHAR(32),
    
    -- Pénz és pontok
    money INT DEFAULT 0,
    dm_points INT DEFAULT 0,
    os_points INT DEFAULT 0,
    dd_points INT DEFAULT 0,
    race_points INT DEFAULT 0,
    shooter_points INT DEFAULT 0,
    hunter_points INT DEFAULT 0,
    tdm_points INT DEFAULT 0,
    
    -- Klán
    Clan VARCHAR(32) DEFAULT NULL,
    
    -- JSON adatok
    data JSON DEFAULT NULL,
    tuning JSON DEFAULT NULL,
    awards JSON DEFAULT NULL,
    
    -- Időbélyegek
    regdate TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Indexek
    INDEX idx_username (username),
    INDEX idx_serial (serial),
    INDEX idx_clan (Clan)
);

-- Ugyanez a fejlesztői adatbázisban
USE v_accounts_dev;

CREATE TABLE IF NOT EXISTS vmtasa_accounts (
    account_id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(32) NOT NULL UNIQUE,
    password_hash VARCHAR(128) NOT NULL,
    email VARCHAR(100),
    serial VARCHAR(32),
    money INT DEFAULT 0,
    dm_points INT DEFAULT 0,
    os_points INT DEFAULT 0,
    dd_points INT DEFAULT 0,
    race_points INT DEFAULT 0,
    shooter_points INT DEFAULT 0,
    hunter_points INT DEFAULT 0,
    tdm_points INT DEFAULT 0,
    Clan VARCHAR(32) DEFAULT NULL,
    data JSON DEFAULT NULL,
    tuning JSON DEFAULT NULL,
    awards JSON DEFAULT NULL,
    regdate TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_serial (serial),
    INDEX idx_clan (Clan)
);
```

### 4. Achievement tábla
```sql
USE v_accounts;

CREATE TABLE IF NOT EXISTS v_achievements (
    id INT PRIMARY KEY AUTO_INCREMENT,
    account_id INT NOT NULL,
    achievement_id INT NOT NULL,
    unlocked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (account_id) REFERENCES vmtasa_accounts(account_id) ON DELETE CASCADE,
    UNIQUE KEY unique_achievement (account_id, achievement_id),
    INDEX idx_account (account_id),
    INDEX idx_achievement (achievement_id)
);

-- Fejlesztői adatbázisban is
USE v_accounts_dev;

CREATE TABLE IF NOT EXISTS v_achievements (
    id INT PRIMARY KEY AUTO_INCREMENT,
    account_id INT NOT NULL,
    achievement_id INT NOT NULL,
    unlocked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (account_id) REFERENCES vmtasa_accounts(account_id) ON DELETE CASCADE,
    UNIQUE KEY unique_achievement (account_id, achievement_id),
    INDEX idx_account (account_id),
    INDEX idx_achievement (achievement_id)
);
```

---

## Kiegészítő táblák

### 1. Klán rendszer táblák
```sql
USE v_accounts;

-- Klánok tábla
CREATE TABLE IF NOT EXISTS v_clans (
    clan_id INT PRIMARY KEY AUTO_INCREMENT,
    clan_name VARCHAR(32) NOT NULL UNIQUE,
    clan_tag VARCHAR(8) NOT NULL UNIQUE,
    leader_id INT NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (leader_id) REFERENCES vmtasa_accounts(account_id),
    INDEX idx_name (clan_name),
    INDEX idx_tag (clan_tag)
);

-- Klán tagság tábla
CREATE TABLE IF NOT EXISTS v_clan_members (
    id INT PRIMARY KEY AUTO_INCREMENT,
    clan_id INT NOT NULL,
    account_id INT NOT NULL,
    rank ENUM('member', 'moderator', 'admin', 'leader') DEFAULT 'member',
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (clan_id) REFERENCES v_clans(clan_id) ON DELETE CASCADE,
    FOREIGN KEY (account_id) REFERENCES vmtasa_accounts(account_id) ON DELETE CASCADE,
    UNIQUE KEY unique_membership (clan_id, account_id)
);
```

### 2. Toptimes táblák (dinamikusan generált)
```sql
USE mta_toptimes;

-- Példa toptimes tábla (minden pályához külön tábla generálódik)
-- A tábla neve MD5 hash lesz (arenaName + mapName alapján)

-- Általános struktúra:
CREATE TABLE IF NOT EXISTS example_map_hash (
    id VARCHAR(33) PRIMARY KEY,
    username VARCHAR(30) NOT NULL,
    nickname VARCHAR(30) NOT NULL,
    serial VARCHAR(33) NOT NULL,
    time INT NOT NULL,
    dateRecorded VARCHAR(50) NOT NULL,
    country VARCHAR(30),
    
    INDEX idx_time (time),
    INDEX idx_username (username),
    INDEX idx_serial (serial)
);
```

### 3. Admin táblák (admin resource-okhoz)
```sql
USE v_accounts;

-- Admin alias tábla
CREATE TABLE IF NOT EXISTS admin_alias (
    id INT PRIMARY KEY AUTO_INCREMENT,
    ip VARCHAR(45) NOT NULL,
    serial VARCHAR(32) NOT NULL,
    name VARCHAR(32) NOT NULL,
    time INTEGER NOT NULL,
    
    INDEX idx_ip (ip),
    INDEX idx_serial (serial)
);

-- Admin figyelmeztetések
CREATE TABLE IF NOT EXISTS admin_warnings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    ip VARCHAR(45) NOT NULL,
    serial VARCHAR(32) NOT NULL,
    name VARCHAR(32) NOT NULL,
    time INTEGER NOT NULL,
    reason TEXT,
    
    INDEX idx_ip (ip),
    INDEX idx_serial (serial)
);

-- Admin screenshot tábla
CREATE TABLE IF NOT EXISTS admin_screenshots (
    id INTEGER PRIMARY KEY AUTO_INCREMENT,
    player TEXT NOT NULL,
    serial TEXT NOT NULL,
    admin TEXT NOT NULL,
    realtime TEXT NOT NULL,
    
    INDEX idx_player (player(32)),
    INDEX idx_serial (serial(32))
);
```

---

## MySQL konfiguráció frissítése

### 1. Resource konfigurációk módosítása

**FONTOS BIZTONSÁGI FIGYELMEZTETÉS:**
A jelenlegi konfigurációban hardkódolt jelszavak vannak! Ezeket módosítani kell.

#### v_mysql resource frissítése
```bash
sudo nano /opt/mta/mods/deathmatch/resources/[vultaic]/v_mysql/mysql_server.lua
```

Keresendő sor (59. sor körül):
```lua
g_Connection, error = dbConnect("mysql", "dbname="..DB_NAME..";host=127.0.0.1;port=3306", "root", "M1RAg3_Zz@ST3R")
```

Módosítás:
```lua
g_Connection, error = dbConnect("mysql", "dbname="..DB_NAME..";host=127.0.0.1;port=3306", "mtauser", "MTA_Strong_Password_2024!")
```

#### Egyéb resource-ok frissítése
```bash
# v_clans resource
sudo nano /opt/mta/mods/deathmatch/resources/[vultaic]/v_clans/clan_server.lua

# v_achievements resource  
sudo nano /opt/mta/mods/deathmatch/resources/[vultaic]/v_achievements/achievements_s.lua

# v_toptimes resource
sudo nano /opt/mta/mods/deathmatch/resources/[vultaic]/v_toptimes/toptimes_server.lua
```

Mindegyikben keresendő és módosítandó:
```lua
-- RÉGI:
dbConnect("mysql", "dbname=...;host=127.0.0.1;port=3306", "root", "M1RAg3_Zz@ST3R")

-- ÚJ:
dbConnect("mysql", "dbname=...;host=127.0.0.1;port=3306", "mtauser", "MTA_Strong_Password_2024!")
```

---

## Adatbázis kapcsolat tesztelése

### 1. Manuális kapcsolat teszt
```bash
# Kapcsolódás az új felhasználóval
mysql -u sunuser -p

# Adatbázisok ellenőrzése
SHOW DATABASES;

# Táblák ellenőrzése
USE v_accounts;
SHOW TABLES;

# Tábla struktúra ellenőrzése
DESCRIBE vmtasa_accounts;
```

### 2. MTA szerver kapcsolat teszt
```bash
# MTA szerver indítása
cd /opt/mta
sudo -u mtaserver ./mta-server64

# Logok ellenőrzése
tail -f /opt/mta/mods/deathmatch/logs/scripts.log | grep -i mysql

# Keresendő üzenetek:
# "MySQL: Connected"
# "Achievements MySQL: Connected"
# "Clans: Connected"
```

### 3. Teszt adatok beszúrása
```sql
USE v_accounts;

-- Teszt felhasználó létrehozása
INSERT INTO vmtasa_accounts (username, password_hash, email, serial) 
VALUES ('testuser', 'testhash123', '<EMAIL>', 'testserial123');

-- Ellenőrzés
SELECT * FROM vmtasa_accounts WHERE username = 'testuser';

-- Törlés
DELETE FROM vmtasa_accounts WHERE username = 'testuser';
```

---

## Biztonsági mentés beállítása

### 1. Automatikus biztonsági mentés script
```bash
sudo nano /opt/scripts/mysql_backup.sh
```

Script tartalma:
```bash
#!/bin/bash

# Konfiguráció
DB_USER="mtauser"
DB_PASS="MTA_Strong_Password_2024!"
BACKUP_DIR="/opt/backups/mysql"
DATE=$(date +%Y%m%d_%H%M%S)

# Könyvtár létrehozása
mkdir -p $BACKUP_DIR

# Adatbázisok mentése
mysqldump -u $DB_USER -p$DB_PASS v_accounts > $BACKUP_DIR/v_accounts_$DATE.sql
mysqldump -u $DB_USER -p$DB_PASS v_accounts_dev > $BACKUP_DIR/v_accounts_dev_$DATE.sql
mysqldump -u $DB_USER -p$DB_PASS mta_toptimes > $BACKUP_DIR/mta_toptimes_$DATE.sql

# Régi mentések törlése (30 napnál régebbiek)
find $BACKUP_DIR -name "*.sql" -mtime +30 -delete

echo "Backup completed: $DATE"
```

```bash
# Jogosultságok beállítása
sudo chmod +x /opt/scripts/mysql_backup.sh

# Crontab beállítása (napi mentés 2:00-kor)
sudo crontab -e

# Hozzáadandó sor:
0 2 * * * /opt/scripts/mysql_backup.sh >> /var/log/mysql_backup.log 2>&1
```

### 2. Mentés visszaállítása
```bash
# Adatbázis visszaállítása mentésből
mysql -u mtauser -p v_accounts < /opt/backups/mysql/v_accounts_20240824_020000.sql
```

---

## Hibaelhárítás

### Gyakori problémák és megoldások

#### 1. "Access denied" hiba
```bash
# Felhasználó és jelszó ellenőrzése
mysql -u mtauser -p

# Jogosultságok ellenőrzése
mysql -u root -p
SHOW GRANTS FOR 'mtauser'@'localhost';
```

#### 2. "Table doesn't exist" hiba
```bash
# Táblák ellenőrzése
mysql -u mtauser -p v_accounts
SHOW TABLES;

# Tábla újralétrehozása
# (használd a fenti CREATE TABLE parancsokat)
```

#### 3. "Connection refused" hiba
```bash
# MySQL szolgáltatás ellenőrzése
sudo systemctl status mariadb

# MySQL újraindítása
sudo systemctl restart mariadb

# Port ellenőrzése
sudo netstat -tulpn | grep 3306
```

#### 4. JSON adatok problémái
```sql
-- JSON validáció
SELECT account_id, username, JSON_VALID(data) as valid_data 
FROM vmtasa_accounts 
WHERE data IS NOT NULL;

-- Hibás JSON javítása
UPDATE vmtasa_accounts 
SET data = '{}' 
WHERE data IS NOT NULL AND NOT JSON_VALID(data);
```

#### 5. Karakterkódolási problémák
```sql
-- Adatbázis karakterkészlet ellenőrzése
SHOW CREATE DATABASE v_accounts;

-- Tábla karakterkészlet ellenőrzése
SHOW CREATE TABLE vmtasa_accounts;

-- Karakterkészlet módosítása
ALTER DATABASE v_accounts CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE vmtasa_accounts CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

---

## Következő lépések

Az adatbázis beállítása után folytasd a következő útmutatókkal:
- **03_Weboldal_es_Forum.md** - Weboldal és fórum telepítése
- **04_Konfiguracio_es_Teszteles.md** - Végső konfiguráció és tesztelés

---

*Ez az útmutató a Vultaic MGM rendszer adatbázis struktúrájának beállítását mutatja be. A biztonsági beállítások és jelszavak módosítása kritikus fontosságú!*
