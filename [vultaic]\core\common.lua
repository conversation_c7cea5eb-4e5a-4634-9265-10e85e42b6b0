--[[
	VULTAIC MGM - KÖZÖS SEGÉDFÜGGVÉNYEK ÉS OSZTÁLYOK

	Ez a fájl tartalmazza a közös segédfüggvényeket és osztályokat,
	amelyeket mind a szerver, mind a kliens oldal használ.

	Főbb komponensek:
	- Countdown osztály: Visszaszámlálás kezel<PERSON>e
	- Timer osztály: Fej<PERSON> timer kezelés
	- Tábla manipuláció függvények
	- Idő formázás függvények
	- Játékos állapot ellenőrzés
]]

--[[
	COUNTDOWN OSZTÁLY

	Ez az osztály visszaszámlálások kezelésére szolgál.
	Hasznos játék indítás előtti visszaszámlálásokhoz.

	Használat:
	local countdown = Countdown:create()
	countdown:start(function(count) print(count) end, 10)
]]

-- Countdown osztály definíció
Countdown = {}
Countdown.__index = Countdown
Countdown.instances = {}

--[[
	Új countdown példány létrehoz<PERSON>a

	@param boolean autoDestroy - Automatikus törlés a befejezés után
	@return Countdown - Az új countdown példány
]]
function Countdown:create(autoDestroy)
	local id = #Countdown.instances + 1
	Countdown.instances[id] = setmetatable(
		{
			id = id,
			timer = nil,
			autoDestroy = autoDestroy,
		},
		self
	)
	return Countdown.instances[id]
end

--[[
	Countdown példány törlése

	Leállítja a timer-t és eltávolítja a példányt a memóriából.
]]
function Countdown:destroy()
	self:stop()
	Countdown.instances[self.id] = nil
	self.id = 0
end

--[[
	Ellenőrzi, hogy a countdown aktív-e

	@return boolean - true ha fut, false ha nem
]]
function Countdown:isActive()
	return self.timer ~= nil
end

--[[
	Countdown leállítása

	Leállítja a timer-t, de nem törli a példányt.
]]
function Countdown:stop()
	if self.timer then
		killTimer(self.timer)
		self.timer = nil
	end
end

--[[
	Countdown indítása

	@param function handledFunction - A meghívandó függvény minden másodpercben
	@param number timesToExecute - Hányszor fusson le (másodpercek)
]]
function Countdown:start(handledFunction, timesToExecute)
	self:stop()
	self.handledFunction = handledFunction
	self.count = timesToExecute
	self.doDestroy = false
	-- Timer indítása 1 másodperces intervallummal
	self.timer = setTimer(function() self:handleFunctionCall() end, 1000, timesToExecute)
end

--[[
	Belső függvény a countdown kezelésére

	Ez a függvény hívódik meg minden másodpercben.
]]
function Countdown:handleFunctionCall()
	if self.count > 0 then
		self.count = self.count - 1
		if self.count == 0 then
			self.timer = nil
			self.doDestroy = self.autoDestroy
		end
	end
	-- Felhasználói függvény meghívása az aktuális számmal
	self.handledFunction(self.count)
	if self.doDestroy then
		self:destroy()
	end
end

--[[
	TIMER OSZTÁLY

	Ez az osztály fejlett timer kezelést biztosít.
	Hasonló a Countdown-hoz, de rugalmasabb intervallumokkal.

	Használat:
	local timer = Timer:create()
	timer:setTimer(function() print("Hello") end, 1000, 5)
]]

-- Timer osztály definíció
Timer = {}
Timer.__index = Timer
Timer.instances = {}

--[[
	Új timer példány létrehozása

	@param boolean autoDestroy - Automatikus törlés a befejezés után
	@return Timer - Az új timer példány
]]
function Timer:create(autoDestroy)
	local id = #Timer.instances + 1
	Timer.instances[id] = setmetatable(
		{
			id = id,
			timer = nil,
			autoDestroy = autoDestroy,
		},
		self
	)
	return Timer.instances[id]
end

--[[
	Timer példány törlése

	Leállítja a timer-t és eltávolítja a példányt a memóriából.
]]
function Timer:destroy()
	self:killTimer()
	Timer.instances[self.id] = nil
	self.id = 0
end

--[[
	Ellenőrzi, hogy a timer aktív-e

	@return boolean - true ha fut, false ha nem
]]
function Timer:isActive()
	return self.timer ~= nil
end

--[[
	Timer részleteinek lekérdezése

	@return table|nil - Timer részletek vagy nil
]]
function Timer:getDetails()
	if self.timer then
		return getTimerDetails(self.timer)
	end
	return nil
end

--[[
	Timer leállítása

	Leállítja a timer-t, de nem törli a példányt.
]]
function Timer:killTimer()
	if self.timer then
		killTimer(self.timer)
		self.timer = nil
	end
end

--[[
	Timer beállítása és indítása

	@param function handledFunction - A meghívandó függvény
	@param number timeInterval - Intervallum milliszekundumban
	@param number timesToExecute - Hányszor fusson le (0 = végtelen)
	@param ... - További argumentumok a függvénynek
]]
function Timer:setTimer(handledFunction, timeInterval, timesToExecute, ...)
	self:killTimer()
	self.handledFunction = handledFunction
	self.count = timesToExecute
	self.doDestroy = false
	self.arguments = {...}
	-- Minimum 50ms intervallum biztosítása
	if type(timeInterval) ~="number" or timeInterval < 50 then
		timeInterval = 50
	end
	self.timer = setTimer(function() self:handleFunctionCall() end, timeInterval, timesToExecute)
end

--[[
	Belső függvény a timer kezelésére

	Ez a függvény hívódik meg minden intervallumban.
]]
function Timer:handleFunctionCall()
	if self.count > 0 then
		self.count = self.count - 1
		if self.count == 0 then
			self.timer = nil
			self.doDestroy = self.autoDestroy
		end
	end
	-- Felhasználói függvény meghívása az argumentumokkal
	self.handledFunction(unpack(self.arguments))
	if self.doDestroy then
		self:destroy()
	end
end

--[[
	TÁBLA MANIPULÁCIÓ FÜGGVÉNYEK

	Ezek a függvények megkönnyítik a táblák kezelését.
]]

--[[
	Érték keresése táblában

	@param table _table - A tábla, amiben keresünk
	@param any value - A keresett érték
	@return number|boolean - Az index vagy false ha nem található
]]
function tableFind(_table, value)
	for i, item in pairs(_table) do
		if item == value then
			return i
		end
	end
	return false
end

--[[
	Érték hozzáadása táblához (ha még nincs benne)

	@param table _table - A cél tábla
	@param any value - A hozzáadandó érték

	Ez a függvény csak akkor adja hozzá az értéket, ha még nincs a táblában.
]]
function tableInsert(_table, value)
	local id = tableFind(_table, value)
	if id then
		return -- Már benne van, nem adjuk hozzá újra
	end
	if type(_table) == "table" and value then
		table.insert(_table, value)
	end
end

--[[
	Érték eltávolítása táblából

	@param table _table - A cél tábla
	@param any value - Az eltávolítandó érték

	Ez a függvény megkeresi és eltávolítja az értéket a táblából.
]]
function tableRemove(_table, value)
	if type(_table) == "table" and value then
		local id = tableFind(_table, value)
		if id then
			table.remove(_table, id)
		end
	end
end

--[[
	IDŐ FORMÁZÁS FÜGGVÉNYEK
]]

--[[
	Milliszekundum konvertálása idő string-gé

	@param number ms - Milliszekundumok
	@return string - Formázott idő string (perc:másodperc:századmásodperc)

	Példa: 65432 ms -> "1:05:43"
]]
function msToTimeString(ms)
	if not ms then
		return ""
	end
	-- Századmásodpercek számítása
	local centiseconds = tostring(math.floor(math.fmod(ms, 1000)/10))
	if #centiseconds == 1 then
		centiseconds = "0"..centiseconds
	end
	-- Másodpercek számítása
	local s = math.floor(ms/1000)
	local seconds = tostring(math.fmod(s, 60))
	if #seconds == 1 then
		seconds = "0"..seconds
	end
	-- Percek számítása
	local minutes = tostring(math.floor(s/60))
	return minutes..":"..seconds..":"..centiseconds
end

--[[
	JÁTÉKOS ÁLLAPOT ELLENŐRZÉS
]]

--[[
	Ellenőrzi, hogy egy játékos halott-e

	@param element player - A játékos element
	@return boolean - true ha halott, false ha él

	Ez a függvény több módszerrel is ellenőrzi a játékos állapotát
	a pontosság érdekében.
]]
function isPlayerDead(player)
	return not getElementHealth(player) or getElementHealth(player) < 1e-45 or isPedDead(player)
end