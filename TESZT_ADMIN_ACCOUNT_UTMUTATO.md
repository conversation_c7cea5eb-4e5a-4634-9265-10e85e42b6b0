# Vultaic MGM - Te<PERSON>t Admin Account Létrehozása

## 🎯 **<PERSON><PERSON>zet Elemzése**

### **<PERSON><PERSON><PERSON><PERSON> Probléma:**
- ✅ **<PERSON><PERSON><PERSON> működik** - <PERSON><PERSON> regisztrálódnak
- ✅ **MySQL kapcsolatok** - Minden adatbázis elérhető
- ❌ **Login rendszer** - Fórum API-ra é<PERSON>ül (még nincs weboldal)
- ❌ **Guest mód** - Korl<PERSON>tozott funkcionalitás
- ❌ **Map betöltés** - Nem tudsz tesztelni bejelentkezett userrel

### **Cél:**
Teszt admin account létrehozása, hogy tovább tudd tesztelni a rendszert.

---

## 🚀 **Aj<PERSON><PERSON>t <PERSON>oldás: Bypass Login Rendszer**

### **Miért Ez a Legjobb Opció:**
1. **Gyors** - 10 perc alatt kész
2. **Biztonságos** - <PERSON><PERSON><PERSON><PERSON> viss<PERSON>llítható
3. **Teljes funkcionali<PERSON>** - <PERSON>min jogo<PERSON>l tesztelhetsz
4. **Nem befolyásolja** a jövőbeli weboldal fejlesztést

---

## 🔧 **1. Lépés: Login Bypass Létrehozása**

### **A) Bypass Script Létrehozása**

**Fájl:** `[vultaic]/v_login/login_server_bypass.lua`

```lua
--[[
    VULTAIC MGM - LOGIN BYPASS (FEJLESZTÉSI CÉLRA)
    
    Ez a fájl átmeneti megoldás a teszteléshez.
    NE használd éles környezetben!
]]

local bypass = {}
bypass.enabled = true

-- Teszt felhasználók
local dummyUsers = {
    ["testadmin"] = {
        password = "admin123",
        connect_id = 1,
        username = "testadmin",
        user_id = 1,
        money = 999999,
        dm_points = 50000,
        os_points = 25000,
        dd_points = 15000,
        race_points = 30000,
        shooter_points = 20000,
        hunter_points = 10000,
        tdm_points = 5000,
        Clan = "AdminClan",
        -- Admin jogosultságok
        moderator = false,
        super_moderator = false,
        community_manager = false,
        developer = true,
        manager = true,
        admin_level = 5
    },
    ["test"] = {
        password = "test123",
        connect_id = 2,
        username = "test",
        user_id = 2,
        money = 50000,
        dm_points = 1000,
        os_points = 800,
        dd_points = 600,
        race_points = 1200,
        shooter_points = 900,
        hunter_points = 400,
        tdm_points = 300,
        Clan = "TestClan"
    }
}

-- Bypass login függvény
function bypass.connectAPI(username, password, player)
    if not bypass.enabled then
        return -- Eredeti API hívás
    end
    
    local user = dummyUsers[username]
    if user and user.password == password then
        -- Sikeres bypass login
        local response = {
            error = 0,
            connect_id = user.connect_id,
            username = user.username,
            user_id = user.user_id,
            moderator = user.moderator or false,
            super_moderator = user.super_moderator or false,
            community_manager = user.community_manager or false,
            developer = user.developer or false,
            manager = user.manager or false,
            admin_level = user.admin_level or 1
        }
        response.password = password
        
        local assign = login.assingUserdata(player, response)
        if assign then
            triggerClientEvent(player, "notification:create", player, "Log in", "Successfully logged in as "..response.username.." (BYPASS MODE)")
            outputDebugString("BYPASS LOGIN: "..username.." logged in successfully")
        else
            triggerClientEvent(player, "notification:create", player, "Log in", "Failed to login as "..response.username)
        end
    else
        triggerClientEvent(player, "notification:create", player, "Log in", "Invalid username or password (BYPASS MODE)")
    end
    
    login.activeAttempts[player] = nil
end

-- Eredeti login függvény felülírása
if bypass.enabled then
    outputDebugString("LOGIN BYPASS MODE ACTIVE - CSAK FEJLESZTÉSHEZ!")
    
    -- Eredeti connectAPI mentése
    login.originalConnectAPI = login.connectAPI
    
    -- Bypass connectAPI használata
    login.connectAPI = bypass.connectAPI
end

-- Bypass info parancs
addCommandHandler("bypass_info", function(player)
    if not bypass.enabled then
        outputChatBox("Bypass mode is disabled", player)
        return
    end
    
    outputChatBox("=== LOGIN BYPASS INFO ===", player, 255, 255, 0)
    outputChatBox("Available test accounts:", player, 255, 255, 255)
    for username, data in pairs(dummyUsers) do
        outputChatBox("- "..username.." / "..data.password.." (Level: "..(data.admin_level or 1)..")", player, 200, 200, 200)
    end
end)

-- Bypass toggle (csak adminoknak)
addCommandHandler("toggle_bypass", function(player)
    local adminLevel = getElementData(player, "admin_level") or 0
    if adminLevel < 3 then
        outputChatBox("Access denied", player, 255, 0, 0)
        return
    end
    
    bypass.enabled = not bypass.enabled
    outputChatBox("Bypass mode: "..(bypass.enabled and "ENABLED" or "DISABLED"), player, 255, 255, 0)
    
    if bypass.enabled then
        login.connectAPI = bypass.connectAPI
    else
        login.connectAPI = login.originalConnectAPI
    end
end)
```

### **B) Meta.xml Módosítása**

**Fájl:** `[vultaic]/v_login/meta.xml`

```xml
<!-- Bypass script hozzáadása -->
<script src="login_server_bypass.lua" type="server"/>
```

---

## 🔧 **2. Lépés: Adatbázis Teszt Account**

### **Alternatív Megoldás: Közvetlen Adatbázis Beszúrás**

Ha a bypass nem működne, közvetlenül az adatbázisba is beszúrhatsz egy teszt accountot:

```sql
-- Kapcsolódás
mysql -u sunuser -p
-- Jelszó: SunUser2025@&#

-- Fejlesztői adatbázis használata
USE v_accounts_dev;

-- Teszt admin account létrehozása
INSERT INTO vmtasa_accounts (
    account_id,
    username, 
    password_hash, 
    email, 
    serial, 
    money, 
    dm_points,
    os_points,
    dd_points,
    race_points,
    shooter_points,
    hunter_points,
    tdm_points,
    Clan,
    data, 
    tuning, 
    awards
) VALUES (
    1,
    'testadmin',
    'bypass_hash_123',
    '<EMAIL>',
    'D3D2AB2339790C36BAA9CCB6A96BBFF4',  -- A te serialod
    1000000,
    50000,
    25000,
    15000,
    30000,
    20000,
    10000,
    5000,
    'AdminClan',
    '{"level": 99, "admin_level": 5}',
    '{}',
    '[]'
);

-- Ellenőrzés
SELECT * FROM vmtasa_accounts WHERE username = 'testadmin';
```

---

## 🎯 **3. Lépés: ACL Admin Jogosultságok**

### **Serial Hozzáadása Admin Csoporthoz**

**Fájl:** `acl.xml`

```xml
<!-- Admin csoportban add hozzá a serialod: -->
<object name="user.D3D2AB2339790C36BAA9CCB6A96BBFF4" />
```

---

## 🚀 **4. Lépés: Tesztelési Folyamat**

### **A) Bypass Módszerrel:**
1. **Bypass script létrehozása** és meta.xml módosítása
2. **Szerver újraindítás** - `restart v_login`
3. **Belépés** - Username: `testadmin`, Password: `admin123`
4. **Admin jogok ellenőrzése** - `/bypass_info` parancs

### **B) Adatbázis Módszerrel:**
1. **SQL script futtatása** - Teszt account beszúrása
2. **ACL serial hozzáadása** - Admin jogosultságok
3. **Szerver újraindítás**
4. **Belépés** - Username: `testadmin`, Password: `bypass_hash_123`

---

## 🔍 **5. Lépés: Map Betöltés Tesztelése**

### **Debug Információk:**
```bash
# Kliens oldali hibák (F8 konzol)
# Szerver oldali debug (scriptdebugloglevel=3)
# Map fájlok ellenőrzése
```

### **Mit Ellenőrizz:**
1. **Map meta.xml fájlok** - Léteznek és helyesek?
2. **Map .map fájlok** - Megfelelő formátum?
3. **Kliens oldali letöltés** - Progress bar megjelenik?
4. **Sandbox betöltés** - Sikeres-e?

---

## 💡 **Ajánlásom**

### **Rövid Távú (Most):**
1. **Bypass login rendszer** - Gyors teszteléshez
2. **Map problémák megoldása** - Debug scriptdebuglevel=3-mal
3. **Alapvető funkciók tesztelése** - Admin jogokkal

### **Hosszú Távú (Később):**
1. **Weboldal fejlesztés** - Teljes regisztrációs rendszer
2. **Fórum integráció** - API végpontok
3. **Bypass eltávolítása** - Éles rendszerre váltás

---

## 🎯 **Következő Lépések**

### **1. Döntés:**
- **Bypass login** → Gyors tesztelés (ajánlott)
- **Weboldal fejlesztés** → Teljes rendszer (hosszabb)

### **2. Implementáció:**
- **Bypass script** létrehozása
- **Meta.xml** módosítása
- **Szerver újraindítás**

### **3. Tesztelés:**
- **Admin belépés** tesztelése
- **Map betöltés** debug-olása
- **Arena funkciók** ellenőrzése

---

## 🎉 **Eredmény**

A bypass rendszerrel:
- ✅ **Teljes admin jogosultságokkal** tesztelhetsz
- ✅ **Map betöltési problémákat** debug-olhatod
- ✅ **Minden arena funkciót** kipróbálhatsz
- ✅ **Weboldal fejlesztést** később végezheted el

**Mit választasz? Bypass login vagy weboldal fejlesztés?** 🤔
