--[[
	VULTAIC MGM - AVATAR RENDSZER SZERVER OLDAL

	Ez a fájl az avatar (profilkép) rendszer szerver oldali logikáját tartalmazza.

	Főbb funkciók:
	- Avatar szinkronizáció a fórumról
	- Avatar cache kezelés
	- Automatikus letöltés és tárolás
	- MD5 hash alapú fájlkezelés

	Működés:
	1. Játékos bejelentkezéskor ellenőrzi az avatar adatokat
	2. MD5 hash-t generál az avatar URL-ből
	3. <PERSON><PERSON><PERSON><PERSON>, hogy a cache-ben megvan-e már
	4. <PERSON> ninc<PERSON>, letölti és elmenti
	5. Beállítja a játékos avatarHash adatát
]]

-- Fórum uploads könyvtár URL-je
local pageURL = "https://forum.vultaic.com/uploads/"

--[[
	Avatar szinkronizáció

	@param table data - <PERSON><PERSON> adatok, benne az avatar információval

	Ez a függvény akkor hívódik meg, amikor egy játékos bejelentkezik.
	Ellenőrzi és szinkronizálja a játékos avatar képét.
]]
function syncAvatar(data)
	-- Adatok validálása
	if type(data) ~= "table" then
		return
	end

	local avatar = data.avatar
	if avatar then
		-- MD5 hash generálása az avatar URL-ből (egyedi azonosítóként)
		avatarHash = md5(tostring(avatar))
		local path = "avatarcache/"..avatarHash

		-- Ellenőrzés, hogy már megvan-e a cache-ben
		if fileExists(path) then
			-- Ha megvan, beállítjuk a hash-t
			setElementData(source, "avatarHash", avatarHash)
			--print(getPlayerName(source).."'s avatar is already stored")
		else
			-- Ha nincs meg, letöltjük
			--print(getPlayerName(source).."'s avatar is not stored, fetching...")
			fetchRemote(pageURL..avatar, 1, 10000, saveAvatar, "", false, source, avatarHash, path)
		end
	end
end
addEvent("login:onPlayerLogin", true)
addEventHandler("login:onPlayerLogin", root, syncAvatar)

--[[
	Avatar mentése

	@param string responseData - A letöltött avatar adat
	@param number errorNo - Hiba kód (0 = sikeres)
	@param element player - A játékos element
	@param string avatarHash - Az avatar MD5 hash-e
	@param string path - A mentési útvonal

	Ez a callback függvény akkor hívódik meg, amikor a fetchRemote befejeződik.
]]
function saveAvatar(responseData, errorNo, player, avatarHash, path)
	if errorNo == 0 then
		-- Ha már létezik a fájl, töröljük
		if fileExists(path) then
			fileDelete(path)
		end

		-- Új fájl létrehozása és mentés
		local avatar = fileCreate(path)
		if avatar then
			fileWrite(avatar, responseData)
			fileClose(avatar)
		end

		-- Avatar hash beállítása (jelenleg kikommentezve)
		--setElementData(player, "avatarHash", avatarHash)
		--print("Downloaded and stored avatar for "..getPlayerName(player))
	end
end