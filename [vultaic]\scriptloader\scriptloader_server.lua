--[[
	VULTAIC MGM - SCRIPTLOADER SZERVER OLDAL

	Ez a fájl a scriptloader rendszer szerver oldali logikáját tartalmazza.

	Főbb funkciók:
	- Kliens-szerver függvény szinkronizáció
	- J<PERSON>rm<PERSON> módosítások kezelése
	- Cache könyvtár be<PERSON>llítás
	- Sandbox biztonsági ellenőrzések

	Szinkronizált függvények:
	- setElementModel: jármű modell változtatás
	- setVehicleHandling: járm<PERSON> kezelhetőség módosítás
	- fixVehicle: jármű javítás

	Biztonsági intézkedések:
	- Element létezés ellenőrzése
	- Jármű befagyasztás állapot ellenőrzése
	- Paraméter validáció
]]

--[[
	SANDBOX FÜGGVÉNY SZINKRONIZÁCIÓ

	Ez az event lehetővé teszi a kliensek számára, hogy biztonságosan
	hívjanak meg szerver oldali függvényeket jármű módosításokhoz.
]]
addEvent("sandbox:syncFunction", true)
addEventHandler("sandbox:syncFunction", root,
function(functionName, ...)
	-- Jármű modell változtatás
	if functionName == "setElementModel" then
		local args = {...}
		local vehicle = args[1]    -- Jármű element
		local model = args[2]      -- Új modell ID

		-- Biztonsági ellenőrzések
		if isElement(vehicle) and not isElementFrozen(vehicle) and model then
			setElementModel(vehicle, model)
			outputDebugString("Synced function >> "..functionName, 0)
		end

	--[[
		Jármű upgrade hozzáadás (jelenleg kikommentezve)

		elseif functionName == "addVehicleUpgrade" then
			local args = {...}
			local vehicle = args[1]
			local upgrade = args[2]
			if isElement(vehicle) and not isElementFrozen(vehicle) and not upgrade then
				addVehicleUpgrade(vehicle, upgrade)
				outputDebugString("Synced function >> "..functionName, 0)
			end
	]]

	-- Jármű kezelhetőség beállítás
	elseif functionName == "setVehicleHandling" then
		local args = {...}
		local vehicle = args[1]    -- Jármű element
		local property = args[2]   -- Kezelhetőség tulajdonság
		local value = args[3]      -- Új érték

		-- Biztonsági ellenőrzések
		if isElement(vehicle) and property and value then
			setVehicleHandling(vehicle, property, value)
			outputDebugString("Synced function >> "..functionName, 0)
		end

	-- Jármű javítás
	elseif functionName == "fixVehicle" then
		local args = {...}
		local vehicle = args[1]    -- Jármű element

		-- Biztonsági ellenőrzés
		if isElement(vehicle) then
			fixVehicle(vehicle)
			outputDebugString("Synced function >> "..functionName, 0)
		end
	end
end)

--[[
	KLIENS RESOURCE INDÍTÁS KEZELÉSE

	Amikor a scriptloader kliens oldala elindul, beállítja a cache könyvtárat
	a szerver port alapján.
]]
addEvent("scriptloader:onClientResourceStart", true)
addEventHandler("scriptloader:onClientResourceStart", resourceRoot, function()
	-- Cache könyvtár beállítása a szerver port alapján
	triggerClientEvent(client, "scriptloader:setCacheDirectory", resourceRoot, getServerPort())
end)