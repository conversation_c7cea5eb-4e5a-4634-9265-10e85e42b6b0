function ClientStarted ()
setSkyGradient( 188, 88, 88, 27, 198, 224 )
setWaterColor( 47 , 224, 27 )
outputChatBox ("Welcome ,good luck and have fun!", 27, 89, 224, true)
outputChatBox ("The Name Of The Music is: ' <PERSON><PERSON><PERSON> <PERSON> <PERSON> '", 27, 224, 50, true)
outputChatBox ("Press 'M' to turn music On/Off", 255, 0, 0, true)

end 

addEventHandler( "onClientResourceStart", getResourceRootElement(getThisResource()), ClientStarted )
