--[[
	VULTAIC MGM - KILLDETECTOR ADDON SZERVER OLDAL

	Ez a fájl a killdetector (ölés érz<PERSON>) addon szerver oldali logikáját tartalmazza.

	Főbb funkciók:
	- Játékos ölések érzékelése
	- Kill üzenetek küldése
	- Arena kill event triggerelése
	- Öngyilkosság kezelése

	Működés:
	1. onPlayerKill event fogadása
	2. <PERSON><PERSON><PERSON><PERSON>ossá<PERSON> vs norm<PERSON><PERSON>különböztetése
	3. Kill üzenetek küldése mindkét játékosnak
	4. Arena kill event triggerelése pontszerzéshez
]]

--[[
	Játékos ölés kezelése

	@param element killer - Gyilkos játékos

	Ez a függvény fut le minden játékos öléskor:
	1. <PERSON><PERSON><PERSON><PERSON>, hogy öngyilkoss<PERSON>g-e
	2. Ha nem, akkor kill üzeneteket küld
	3. Arena kill eventet triggerel
]]
function onPlayer<PERSON>ill(killer)
	-- Ö<PERSON><PERSON><PERSON><PERSON><PERSON>g ellen<PERSON>
	if(killer == client) then
		-- <PERSON>ng<PERSON>lkoss<PERSON><PERSON> esetén nincs teend<PERSON> (kommentezett chat üzenet)
		--outputChatBox(("#19846dKill :: #FFFFFF%s#FFFFFF killed himself!"):format(getPlayerName(killer)), getElementParent(killer), 255, 255, 255, true);
	else
		-- Normál ölés esetén:
		-- Kill üzenet küldése az áldozatnak (killer információval)
		triggerClientEvent(client, "killmessage:create", client, {player = killer, action = "killer"})

		-- Kill üzenet küldése a gyilkosnak (killed információval)
		triggerClientEvent(killer, "killmessage:create", killer, {player = client, action = "killed"})

		-- Arena kill event triggerelése pontszerzéshez
		triggerEvent("onArenaKill", getElementParent(killer), killer, player)
	end
end

-- Event regisztrálása
addEvent("onPlayerKill", true)
addEventHandler("onPlayerKill", root, onPlayerKill)